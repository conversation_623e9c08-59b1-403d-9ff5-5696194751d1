
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unlocking the Secrets of Protein Structure and Folding</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&display=swap');

        :root {
            --background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            --text-color: #ffffff;
            --accent-color: #0f3460;
            --secondary-color: #e94560;

            /* Additional elegant color variables */
            --subtle-accent: rgba(15, 52, 96, 0.2);
            --highlight-color: #f1c40f;
            --muted-text: rgba(255, 255, 255, 0.7);
            --shadow-color: rgba(0, 0, 0, 0.2);

            /* Typography variables */
            --heading-font: 'Cormorant Garamond', 'Playfair Display', serif;
            --body-font: 'Poppins', sans-serif;
            --code-font: 'Fira Code', monospace;

            /* Spacing variables */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 2rem;
            --spacing-xl: 4rem;

            /* Animation variables */
            --transition-fast: 0.3s ease;
            --transition-medium: 0.5s ease;
            --transition-slow: 0.8s cubic-bezier(0.77, 0, 0.175, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--body-font);
            background: var(--background);
            color: var(--text-color);
            overflow: hidden;
            transition: background var(--transition-medium);
            line-height: 1.6;
            font-weight: 300;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .presentation-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: var(--spacing-xl) var(--spacing-xl);
            opacity: 0;
            transform: translateY(50px);
            transition: all var(--transition-slow);
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--accent-color) transparent;
            background-size: cover;
            background-position: center;
        }

        /* Custom scrollbar styling */
        .slide::-webkit-scrollbar {
            width: 6px;
        }

        .slide::-webkit-scrollbar-track {
            background: transparent;
        }

        .slide::-webkit-scrollbar-thumb {
            background-color: var(--accent-color);
            border-radius: 3px;
        }

        .slide.active {
            opacity: 1;
            transform: translateY(0);
            animation: slideFadeIn var(--transition-slow) forwards;
        }

        @keyframes slideFadeIn {
            0% { opacity: 0; transform: translateY(30px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--subtle-accent) 0%, transparent 50%);
            opacity: 0.5;
            z-index: -1;
        }

        .slide-header {
            position: absolute;
            top: var(--spacing-lg);
            right: var(--spacing-lg);
            z-index: 10;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .slide-number {
            font-size: 1rem;
            opacity: 0.6;
            font-weight: 300;
            font-family: var(--heading-font);
            letter-spacing: 1px;
            background: var(--subtle-accent);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: 20px;
            min-width: 2.5rem;
            text-align: center;
        }

        .slide-title {
            font-family: var(--heading-font);
            font-size: 3.8rem;
            font-weight: 600;
            margin-bottom: var(--spacing-lg);
            color: var(--accent-color);
            position: relative;
            display: inline-block;
            max-width: 90%;
            word-wrap: break-word;
            line-height: 1.2;
            letter-spacing: -0.5px;
            text-shadow: 1px 1px 2px var(--shadow-color);
        }

        .slide-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 100px;
            height: 3px;
            background: linear-gradient(to right, var(--accent-color), transparent);
            transform-origin: left;
            animation: expandWidth 1.5s ease forwards;
        }

        @keyframes expandWidth {
            0% { width: 0; opacity: 0; }
            100% { width: 100px; opacity: 1; }
        }

        .content {
            font-size: 1.25rem;
            line-height: 1.6;
            max-width: 900px;
            width: 100%;
            overflow-wrap: break-word;
            word-wrap: break-word;
            hyphens: auto;
            margin-bottom: var(--spacing-lg);
            position: relative;
            z-index: 1;
        }

        /* Add elegant styling to content elements */
        .elegant-paragraph {
            margin-bottom: var(--spacing-md);
            font-weight: 300;
            color: var(--text-color);
            text-align: justify;
            text-justify: inter-word;
        }

        .elegant-paragraph:first-letter {
            font-size: 1.5em;
            font-weight: 500;
            color: var(--accent-color);
            font-family: var(--heading-font);
        }

        .elegant-list {
            margin: var(--spacing-md) 0;
            padding-left: var(--spacing-lg);
        }

        .elegant-list li {
            margin-bottom: var(--spacing-sm);
            position: relative;
        }

        .elegant-list li::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 10px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--accent-color);
        }

        .nested-list {
            margin-top: var(--spacing-sm);
            margin-left: var(--spacing-md);
        }

        .elegant-quote {
            border-left: 3px solid var(--accent-color);
            padding: var(--spacing-md) var(--spacing-lg);
            margin: var(--spacing-md) 0;
            background: var(--subtle-accent);
            border-radius: 0 8px 8px 0;
            font-style: italic;
            position: relative;
        }

        .elegant-quote::before {
            content: '"';
            font-family: var(--heading-font);
            font-size: 4rem;
            position: absolute;
            top: -20px;
            left: 10px;
            color: var(--accent-color);
            opacity: 0.2;
        }

        .elegant-hr {
            border: none;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--accent-color), transparent);
            margin: var(--spacing-lg) 0;
        }

        .elegant-hr.thick {
            height: 3px;
        }

        .elegant-image {
            margin: var(--spacing-md) 0;
            position: relative;
            display: inline-block;
            max-width: 100%;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px var(--shadow-color);
        }

        .elegant-image img {
            max-width: 100%;
            display: block;
            transition: transform var(--transition-medium);
        }

        .elegant-image:hover img {
            transform: scale(1.02);
        }

        .elegant-image figcaption {
            padding: var(--spacing-sm);
            text-align: center;
            font-style: italic;
            font-size: 0.9rem;
            color: var(--muted-text);
        }

        .table-container {
            overflow-x: auto;
            margin: var(--spacing-md) 0;
            border-radius: 8px;
            box-shadow: 0 4px 12px var(--shadow-color);
        }

        .elegant-table {
            width: 100%;
            border-collapse: collapse;
            overflow: hidden;
        }

        .elegant-table th, .elegant-table td {
            padding: var(--spacing-sm) var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--subtle-accent);
        }

        .elegant-table th {
            background: var(--subtle-accent);
            font-weight: 600;
            color: var(--accent-color);
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 1px;
        }

        .elegant-table tr:last-child td {
            border-bottom: none;
        }

        .elegant-table tr:hover {
            background: var(--subtle-accent);
        }

        .code-block {
            margin: var(--spacing-md) 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px var(--shadow-color);
            position: relative;
        }

        .code-block code {
            font-family: var(--code-font);
            font-size: 0.9rem;
            line-height: 1.5;
            display: block;
            padding: var(--spacing-md);
            overflow-x: auto;
        }

        .line-number {
            display: inline-block;
            width: 2rem;
            text-align: right;
            color: var(--muted-text);
            margin-right: var(--spacing-sm);
            user-select: none;
        }

        .inline-code {
            font-family: var(--code-font);
            background: var(--subtle-accent);
            padding: 0.1em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .task-list {
            list-style: none;
            padding-left: var(--spacing-md);
            margin: var(--spacing-md) 0;
        }

        .task-item {
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
        }

        .checkbox {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 50%;
            margin-right: var(--spacing-sm);
            background: var(--subtle-accent);
            color: var(--accent-color);
            font-size: 0.8rem;
        }

        .task-item.completed {
            text-decoration: line-through;
            opacity: 0.7;
        }

        /* Navigation hints for multi-part slides */
        .slide-part-indicator {
            position: absolute;
            bottom: 4rem;
            right: 2rem;
            background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 30px;
            font-size: 0.8rem;
            opacity: 0.8;
            transition: all var(--transition-fast);
            box-shadow: 0 4px 12px var(--shadow-color);
            transform: translateY(0);
        }

        .slide-part-indicator:hover {
            opacity: 1;
            transform: translateY(-3px);
            box-shadow: 0 6px 16px var(--shadow-color);
        }

        /* Markdown-rendered HTML elements styling */
        .content h1, .content h2, .content h3, .content h4, .content h5, .content h6 {
            margin-top: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            color: var(--secondary-color);
            font-family: var(--heading-font);
            font-weight: 600;
            line-height: 1.2;
            letter-spacing: -0.5px;
            position: relative;
            display: inline-block;
        }

        .content h1::after, .content h2::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(to right, var(--accent-color), transparent);
        }

        .content h1 {
            font-size: 2.5rem;
            margin-top: var(--spacing-xl);
        }

        .content h2 {
            font-size: 2rem;
            color: var(--accent-color);
        }

        .content h3 {
            font-size: 1.75rem;
            font-weight: 500;
        }

        .content h4 {
            font-size: 1.5rem;
            font-weight: 500;
            color: var(--accent-color);
            opacity: 0.9;
        }

        .content h5 {
            font-size: 1.25rem;
            font-weight: 500;
            font-style: italic;
        }

        .content h6 {
            font-size: 1rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .content p {
            margin-bottom: var(--spacing-md);
            line-height: 1.7;
        }

        .content a {
            color: var(--accent-color);
            text-decoration: none;
            border-bottom: 1px dotted var(--accent-color);
            transition: all var(--transition-fast);
            position: relative;
            display: inline-block;
            padding: 0 2px;
        }

        .content a:hover {
            border-bottom: 1px solid var(--accent-color);
            background: var(--subtle-accent);
            border-radius: 3px;
        }

        .content a::after {
            content: '↗';
            font-size: 0.8em;
            position: relative;
            top: -0.5em;
            opacity: 0;
            margin-left: 2px;
            transition: opacity var(--transition-fast);
        }

        .content a:hover::after {
            opacity: 1;
        }

        .content strong {
            font-weight: 600;
            color: var(--accent-color);
            padding: 0 2px;
        }

        .content em {
            font-style: italic;
            color: var(--secondary-color);
        }

        .content del {
            text-decoration: line-through;
            opacity: 0.7;
        }

        /* Lists are now styled with .elegant-list class */

        /* Images are now styled with .elegant-image class */

        /* Blockquotes are now styled with .elegant-quote class */

        /* Code blocks are now styled with .code-block class */

        /* Tables are now styled with .elegant-table class */

        /* Add animation for content elements */
        .content > * {
            animation: fadeSlideUp 0.8s ease forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        @keyframes fadeSlideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-footer {
            position: absolute;
            bottom: var(--spacing-lg);
            left: 0;
            width: 100%;
            padding: 0 var(--spacing-xl);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .progress-indicator {
            width: 100%;
            height: 4px;
            background: var(--subtle-accent);
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(to right, var(--accent-color), var(--secondary-color));
            transition: width var(--transition-medium);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                rgba(255,255,255,0) 0%,
                rgba(255,255,255,0.3) 50%,
                rgba(255,255,255,0) 100%
            );
            animation: shimmer 2s infinite;
            transform: translateX(-100%);
        }

        @keyframes shimmer {
            100% { transform: translateX(100%); }
        }

        .controls {
            position: fixed;
            bottom: var(--spacing-lg);
            right: var(--spacing-lg);
            z-index: 100;
            display: flex;
            gap: var(--spacing-xs);
            background: rgba(0, 0, 0, 0.1);
            padding: var(--spacing-xs);
            border-radius: 50px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px var(--shadow-color);
        }

        .control-button {
            background: var(--subtle-accent);
            color: var(--text-color);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: 0.9rem;
        }

        .control-button:hover {
            background: var(--accent-color);
            transform: scale(1.1);
            color: white;
            box-shadow: 0 2px 10px var(--shadow-color);
        }

        .control-button:active {
            transform: scale(0.95);
        }

        .theme-switcher {
            position: fixed;
            top: var(--spacing-lg);
            left: var(--spacing-lg);
            z-index: 100;
        }

        .theme-button {
            background: var(--subtle-accent);
            color: var(--text-color);
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 30px;
            cursor: pointer;
            transition: all var(--transition-fast);
            backdrop-filter: blur(10px);
            font-size: 0.9rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 12px var(--shadow-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .theme-button::before {
            content: '🎨';
            font-size: 1rem;
        }

        .theme-button:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px var(--shadow-color);
        }

        .theme-button:active {
            transform: translateY(0);
        }

        /* Animations for content */
        .fade-in {
            animation: fadeIn 1s ease forwards;
            animation-delay: calc(var(--animation-order, 0) * 0.1s);
            opacity: 0;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Elegant transitions between slides */
        .slide.prev {
            transform: translateX(-50px);
            opacity: 0;
            transition: all var(--transition-slow);
        }

        .slide.next {
            transform: translateX(50px);
            opacity: 0;
            transition: all var(--transition-slow);
        }

        /* Elegant background patterns */
        .slide::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background-image: radial-gradient(circle, var(--subtle-accent) 10%, transparent 10.5%);
            background-size: 20px 20px;
            background-position: 0 0;
            opacity: 0.3;
            z-index: -1;
            pointer-events: none;
        }

        /* Responsive adjustments with CSS variables */
        @media (max-width: 1200px) {
            :root {
                --spacing-xl: 3rem;
                --spacing-lg: 1.5rem;
            }

            .slide {
                padding: var(--spacing-xl);
            }

            .slide-title {
                font-size: 3.2rem;
                max-width: 95%;
            }

            .content {
                font-size: 1.15rem;
            }

            .elegant-paragraph:first-letter {
                font-size: 1.3em;
            }
        }

        @media (max-width: 992px) {
            :root {
                --spacing-xl: 2.5rem;
                --spacing-lg: 1.25rem;
                --spacing-md: 0.75rem;
            }

            .slide {
                padding: var(--spacing-xl);
            }

            .slide-title {
                font-size: 2.8rem;
            }

            .content {
                font-size: 1.1rem;
                max-width: 100%;
            }

            .slide-footer {
                padding: 0 var(--spacing-xl);
            }

            .elegant-quote::before {
                font-size: 3rem;
            }

            .slide::after {
                width: 200px;
                height: 200px;
            }
        }

        @media (max-width: 768px) {
            :root {
                --spacing-xl: 2rem;
                --spacing-lg: 1rem;
                --spacing-md: 0.5rem;
            }

            .slide {
                padding: var(--spacing-xl);
            }

            .slide-title {
                font-size: 2.5rem;
                margin-bottom: var(--spacing-md);
            }

            .content {
                font-size: 1rem;
            }

            .slide-footer {
                padding: 0 var(--spacing-xl);
                bottom: var(--spacing-lg);
            }

            .controls {
                bottom: var(--spacing-lg);
                right: var(--spacing-lg);
            }

            .theme-switcher {
                top: var(--spacing-lg);
                left: var(--spacing-lg);
            }

            .elegant-image figcaption {
                font-size: 0.8rem;
            }

            .slide::after {
                width: 150px;
                height: 150px;
                background-size: 15px 15px;
            }
        }

        @media (max-width: 576px) {
            :root {
                --spacing-xl: 1.5rem;
                --spacing-lg: 0.75rem;
                --spacing-md: 0.5rem;
                --spacing-sm: 0.25rem;
            }

            .slide {
                padding: var(--spacing-xl);
            }

            .slide-title {
                font-size: 2rem;
            }

            .content {
                font-size: 0.9rem;
            }

            .slide-header {
                top: var(--spacing-md);
                right: var(--spacing-md);
            }

            .controls {
                bottom: var(--spacing-md);
                right: var(--spacing-md);
                gap: 2px;
                padding: 4px;
            }

            .theme-switcher {
                top: var(--spacing-md);
                left: var(--spacing-md);
            }

            .control-button {
                width: 32px;
                height: 32px;
                font-size: 0.8rem;
            }

            .theme-button {
                font-size: 0.8rem;
                padding: 4px 8px;
            }

            .theme-button::before {
                font-size: 0.9rem;
            }

            .slide-part-indicator {
                bottom: var(--spacing-xl);
                right: var(--spacing-md);
                font-size: 0.7rem;
                padding: 4px 8px;
            }

            .slide::after {
                width: 100px;
                height: 100px;
                background-size: 10px 10px;
            }
        }

        /* Handle very small screens and orientation */
        @media (max-height: 600px), (max-width: 400px) {
            .slide-title {
                font-size: 1.8rem;
                margin-bottom: var(--spacing-sm);
            }

            .content {
                font-size: 0.85rem;
                line-height: 1.4;
            }

            .content h1 { font-size: 1.6rem; }
            .content h2 { font-size: 1.4rem; }
            .content h3 { font-size: 1.2rem; }
            .content h4 { font-size: 1rem; }
            .content h5 { font-size: 0.9rem; }
            .content h6 { font-size: 0.8rem; }

            .elegant-paragraph:first-letter {
                font-size: 1.1em;
            }

            .elegant-quote {
                padding: var(--spacing-sm);
            }

            .elegant-quote::before {
                font-size: 2rem;
                top: -10px;
            }

            .slide::after {
                display: none;
            }
        }

        /* Print styles for PDF export */
        @media print {
            .slide {
                page-break-after: always;
                opacity: 1 !important;
                transform: none !important;
                position: relative;
                height: auto;
                min-height: 100vh;
            }

            .controls, .theme-switcher, .slide-part-indicator {
                display: none !important;
            }

            body {
                background: white !important;
                color: black !important;
            }

            .slide-footer {
                position: relative;
                margin-top: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">

<div class="slide" id="slide-1" data-slide-number="1">
    <div class="slide-header">
        <span class="slide-number">1</span>
    </div>
    <h1 class="slide-title">Introduction: The Amazing World of Proteins</h1>
    <div class="content">
        
<h1 id="introduction-the-amazing-world-of-proteins">Introduction: The Amazing World of Proteins</h1>
<ul class="elegant-list">
<li><strong>Proteins: The Workhorses of the Cell</strong></li>
<ul class="nested-list">
<li>Essential for all life functions.</li>
<li>Built from amino acids linked by peptide bonds.</li>
</ul>
<li><strong>Diverse Functions</strong></li>
<ul class="nested-list">
<li><strong>Enzymes:</strong> Catalyze biochemical reactions (e.g., amylase, lysozyme). <em>Visual: Image of an enzyme catalyzing a reaction.</em></li>
<li><strong>Structural Components:</strong> Provide cell shape and support (e.g., collagen, keratin). <em>Visual: Image of collagen fibers or keratin structure.</em></li>
<li><strong>Signaling:</strong> Transmit signals within and between cells (e.g., insulin, growth factors). <em>Visual: Illustration of a signaling pathway with a protein receptor.</em></li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-2" data-slide-number="2">
    <div class="slide-header">
        <span class="slide-number">2</span>
    </div>
    <h1 class="slide-title">Introduction: The Amazing World of Proteins (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>The Central Dogma: DNA -&gt; RNA -&gt; Protein</strong></li>
<ul class="nested-list">
<li>The fundamental flow of genetic information.</li>
<li>Proteins are the ultimate product of gene expression. <em>Visual: Simplified diagram of the central dogma.</em></li>
</ul>
<li><strong>Presentation Roadmap: Exploring Protein Structure and Folding</strong></li>
<ul class="nested-list">
<li>Primary, secondary, tertiary, and quaternary structures.</li>
<li>Forces that govern protein folding (hydrogen bonds, hydrophobic interactions, etc.).</li>
<li>The protein folding problem and energy landscape. <em>Visual: A roadmap graphic depicting the journey through protein structure topics.</em></li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-3" data-slide-number="3">
    <div class="slide-header">
        <span class="slide-number">3</span>
    </div>
    <h1 class="slide-title">The Building Blocks: Amino Acids</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>The 20 Building Blocks of Life:</strong> Introduction to the common amino acids that form the basis of all proteins.</li>
<ul class="nested-list">
<li>Each with a unique role determined by its structure and properties.</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-4" data-slide-number="4">
    <div class="slide-header">
        <span class="slide-number">4</span>
    </div>
    <h1 class="slide-title">The Building Blocks: Amino Acids (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Amino Acid Structure:</strong></li>
<ul class="nested-list">
<li>Central carbon (α-carbon)</li>
<li>Amino group (-NH₂)</li>
<li>Carboxyl group (-COOH)</li>
<li>Hydrogen atom (-H)</li>
<li><strong>Side Chain (R-group):</strong> <em>The key differentiator between amino acids!</em></li>
<ul class="nested-list">
<li><strong>Visual:</strong> Show a generic amino acid structure highlighting each group.</li>
</ul>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-5" data-slide-number="5">
    <div class="slide-header">
        <span class="slide-number">5</span>
    </div>
    <h1 class="slide-title">The Building Blocks: Amino Acids (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>R-Group Properties are Key:</strong></li>
<ul class="nested-list">
<li><strong>Hydrophobic:</strong> Water-repelling (e.g., Alanine, Valine, Leucine, Isoleucine, Phenylalanine)</li>
<ul class="nested-list">
<li>Tend to cluster in the protein's interior.</li>
</ul>
<li><strong>Hydrophilic:</strong> Water-attracting (e.g., Serine, Threonine, Cysteine, Asparagine, Glutamine)</li>
<ul class="nested-list">
<li>Often found on the protein's surface.</li>
</ul>
<li><strong>Charged:</strong> Acidic (negatively charged) or Basic (positively charged) (e.g., Aspartic acid, Glutamic acid, Lysine, Arginine, Histidine)</li>
<ul class="nested-list">
<li>Play important roles in protein interactions.</li>
<li><strong>Visual:</strong> A small table summarizing the 3 R-group properties with 2-3 examples each. Highlight the R-group on each amino acid.</li>
</ul>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-6" data-slide-number="6">
    <div class="slide-header">
        <span class="slide-number">6</span>
    </div>
    <h1 class="slide-title">The Building Blocks: Amino Acids (Part 4)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Linking Amino Acids: Peptide Bonds</strong></li>
<ul class="nested-list">
<li>Covalent bond formed between the carboxyl group of one amino acid and the amino group of another, releasing water (H₂O).</li>
<li>Forms the protein backbone.</li>
<ul class="nested-list">
<li><strong>Visual:</strong> Show a diagram of peptide bond formation between two amino acids, clearly highlighting the bond and the water molecule released. Label N-terminus and C-terminus.</li>
</ul>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-7" data-slide-number="7">
    <div class="slide-header">
        <span class="slide-number">7</span>
    </div>
    <h1 class="slide-title">Levels of Protein Structure: Primary & Secondary</h1>
    <div class="content">
        <p class="elegant-paragraph"><em>Levels of Protein Structure: Primary &amp; Secondary</em>
<ul class="elegant-list">
<li><strong>Primary Structure: The Amino Acid Sequence</strong></li>
<ul class="nested-list">
<li>Linear sequence of amino acids linked by peptide bonds.</li>
<li>Determined by the gene sequence.</li>
<li>Example: Ala-Gly-Tyr-Lys-Glu...</li>
<li>Visual: Simple diagram showing amino acids linked in a chain, highlighting the peptide bond.</li>
</ul>
</ul></p>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-8" data-slide-number="8">
    <div class="slide-header">
        <span class="slide-number">8</span>
    </div>
    <h1 class="slide-title">Levels of Protein Structure: Primary & Secondary (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Peptide Bond Formation &amp; the Protein Backbone</strong></li>
<ul class="nested-list">
<li>Peptide bond: Covalent bond between the carboxyl group of one amino acid and the amino group of another, releasing water.</li>
<li>The repeating N-Cα-C atoms form the polypeptide backbone.</li>
<li>Visual: Detailed chemical structure of a peptide bond and illustration of the protein backbone.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-9" data-slide-number="9">
    <div class="slide-header">
        <span class="slide-number">9</span>
    </div>
    <h1 class="slide-title">Levels of Protein Structure: Primary & Secondary (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Secondary Structure: Localized Folding</strong></li>
<ul class="nested-list">
<li>Regular, repeating conformations stabilized by hydrogen bonds between backbone atoms.</li>
<li>Common types:</li>
<ul class="nested-list">
<li><strong>Alpha-Helices:</strong> Coiled structure, ~3.6 amino acids per turn.</li>
<ul class="nested-list">
<li>Visual: Cartoon representation of an alpha-helix, showing hydrogen bonds.</li>
</ul>
<li><strong>Beta-Sheets:</strong> Extended strands arranged side-by-side; can be parallel or anti-parallel.</li>
<ul class="nested-list">
<li>Visual: Cartoon representation of a beta-sheet (both parallel and anti-parallel), showing hydrogen bonds.</li>
</ul>
<li><strong>Loops/Turns:</strong> Irregular structures connecting alpha-helices and beta-sheets.</li>
</ul>
<li>Visual: Overview showing an alpha helix, a beta-sheet, and a loop in a single protein.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-10" data-slide-number="10">
    <div class="slide-header">
        <span class="slide-number">10</span>
    </div>
    <h1 class="slide-title">Levels of Protein Structure: Primary & Secondary (Part 4)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Stabilizing Interactions (Hydrogen Bonds)</strong></li>
<ul class="nested-list">
<li><strong>Alpha-Helices:</strong> Hydrogen bonds form between the carbonyl oxygen of residue <em>i</em> and the amide hydrogen of residue <em>i+4</em>.</li>
<ul class="nested-list">
<li>Visual: Zoomed-in view of an alpha-helix highlighting the i to i+4 hydrogen bonding pattern.</li>
</ul>
<li><strong>Beta-Sheets:</strong> Hydrogen bonds form between strands in the sheet, either in parallel or anti-parallel orientation.</li>
<ul class="nested-list">
<li>Visual: Illustrate hydrogen bonding pattern in beta-sheets (parallel and antiparallel).</li>
</ul>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-11" data-slide-number="11">
    <div class="slide-header">
        <span class="slide-number">11</span>
    </div>
    <h1 class="slide-title">Levels of Protein Structure: Tertiary & Quaternary</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Tertiary Structure: The 3D Shape</strong></li>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-12" data-slide-number="12">
    <div class="slide-header">
        <span class="slide-number">12</span>
    </div>
    <h1 class="slide-title">Levels of Protein Structure: Tertiary & Quaternary (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li>Overall spatial arrangement of <em>one</em> polypeptide chain.</li>
<ul class="nested-list">
<li>Driven by the protein trying to achieve the lowest energy state.</li>
<li><strong>Forces stabilizing the structure:</strong></li>
<ul class="nested-list">
<li><strong>Hydrophobic effect:</strong> Nonpolar side chains cluster inside.</li>
<li><strong>Hydrogen bonds:</strong> Between side chains and/or backbone.</li>
<li><strong>Disulfide bridges:</strong> Covalent bonds between cysteine residues.</li>
<li>Ionic bonds</li>
</ul>
<li><strong>Visual:</strong> A 3D structure of myoglobin, highlighting the hydrophobic core and polar surface residues. Show also a close up on a disulfide bridge.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-13" data-slide-number="13">
    <div class="slide-header">
        <span class="slide-number">13</span>
    </div>
    <h1 class="slide-title">Levels of Protein Structure: Tertiary & Quaternary (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Quaternary Structure: Protein Complexes</strong></li>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-14" data-slide-number="14">
    <div class="slide-header">
        <span class="slide-number">14</span>
    </div>
    <h1 class="slide-title">Levels of Protein Structure: Tertiary & Quaternary (Part 4)</h1>
    <div class="content">
        <ul class="elegant-list">
<li>Arrangement of <em>multiple</em> polypeptide chains (subunits) to form a functional protein.</li>
<ul class="nested-list">
<li>Subunits can be identical or different.</li>
<li>Held together by non-covalent interactions.</li>
<li><strong>Example: Hemoglobin</strong></li>
<ul class="nested-list">
<li>A tetramer consisting of two α-globin and two β-globin subunits.</li>
<li>Each subunit binds one molecule of oxygen.</li>
</ul>
<li><strong>Visual:</strong> Show the structure of hemoglobin with labeled alpha and beta subunits and the heme groups with oxygen molecules bound. Include a diagram depicting the arrangement of subunits in a generic multimeric protein.</li>
</ul>
<li><strong>Data point</strong>: Many proteins require quaternary structure to function properly.</li>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-15" data-slide-number="15">
    <div class="slide-header">
        <span class="slide-number">15</span>
    </div>
    <h1 class="slide-title">Protein Backbone Conformation: Torsion Angles and the...</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Backbone Conformation &amp; Torsion Angles</strong></li>
<ul class="nested-list">
<li>Protein backbone defined by repeating N-Cα-C units.</li>
<li><strong>Phi (ϕ):</strong> Rotation around the N-Cα bond.</li>
<li><strong>Psi (ψ):</strong> Rotation around the Cα-C bond.</li>
<li><strong>Omega (ω):</strong> Rotation around the C-N bond (usually 180° due to partial double-bond character).</li>
</ul>
<li><strong>Steric Constraints</strong></li>
<ul class="nested-list">
<li>Not all ϕ/ψ angle combinations are possible due to steric hindrance.</li>
<li>Bulky side chains limit the allowable conformations.</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-16" data-slide-number="16">
    <div class="slide-header">
        <span class="slide-number">16</span>
    </div>
    <h1 class="slide-title">Protein Backbone Conformation: Torsion Angles and the...</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>The Ramachandran Plot</strong></li>
<ul class="nested-list">
<li>A plot of allowed ϕ and ψ angles for amino acid residues in a protein.</li>
<li>Illustrates sterically allowed regions (favored conformations).</li>
<li>Different regions correspond to different secondary structures (e.g., α-helix, β-sheet).</li>
<li>Glycine has the fewest steric constraints and occupies a larger area of the plot.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-17" data-slide-number="17">
    <div class="slide-header">
        <span class="slide-number">17</span>
    </div>
    <h1 class="slide-title">Protein Backbone Conformation: Torsion Angles and the...</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Ramachandran Plot for Structure Validation</strong></li>
<ul class="nested-list">
<li>Essential tool for validating protein structures determined by X-ray crystallography or NMR.</li>
<li>Residues outside the allowed regions indicate potential errors in the structure.</li>
<li>A good model typically has &gt;90% of residues in the favored regions.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-18" data-slide-number="18">
    <div class="slide-header">
        <span class="slide-number">18</span>
    </div>
    <h1 class="slide-title">Protein Backbone Conformation: Torsion Angles and the...</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Visual Suggestions:</strong></li>
<ul class="nested-list">
<li>Diagram showing the phi, psi, and omega angles along a peptide backbone.</li>
<li>A Ramachandran plot showing favored regions for alpha-helices and beta-sheets, and outlier regions. Highlighted glycine region</li>
<li>Overlay a real Ramachandran plot from a solved crystal structure to show real-world data.</li>
<li>3D protein structure with phi/psi angles labeled dynamically.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-19" data-slide-number="19">
    <div class="slide-header">
        <span class="slide-number">19</span>
    </div>
    <h1 class="slide-title">The Protein Folding Problem: A Grand Challenge</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>The Protein Folding Problem: A Grand Challenge</strong></li>
<li><strong>Levinthal's Paradox:</strong></li>
<ul class="nested-list">
<li>Proteins must fold into their native 3D structure to function.</li>
<li>Enormous number of possible conformations.</li>
<li>Levinthal estimated it would take longer than the age of the universe for a protein to randomly sample all possible conformations.</li>
<li><em>Visual: Illustration comparing the vast number of potential conformations to the single native state.</em></li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-20" data-slide-number="20">
    <div class="slide-header">
        <span class="slide-number">20</span>
    </div>
    <h1 class="slide-title">The Protein Folding Problem: A Grand Challenge (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>The Challenge:</strong></li>
<ul class="nested-list">
<li>Predicting a protein's 3D structure from its amino acid sequence remains a major challenge in biology.</li>
<li>Understanding the rules governing protein folding could revolutionize drug design and materials science.</li>
<li><em>Visual: Image of a protein sequence on one side, and the folded 3D structure on the other, with a question mark in between.</em></li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-21" data-slide-number="21">
    <div class="slide-header">
        <span class="slide-number">21</span>
    </div>
    <h1 class="slide-title">The Protein Folding Problem: A Grand Challenge (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Anfinsen's Experiment (1950s): Sequence Determines Structure</strong></li>
<ul class="nested-list">
<li>Ribonuclease A unfolds and loses activity in denaturing conditions (urea).</li>
<li>Removal of urea allows spontaneous refolding and recovery of enzymatic activity.</li>
<li>Demonstrates that the amino acid sequence contains all the information needed for correct folding.</li>
<li><em>Visual: Diagram of Anfinsen's experiment showing the unfolding and refolding of ribonuclease A.</em></li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-22" data-slide-number="22">
    <div class="slide-header">
        <span class="slide-number">22</span>
    </div>
    <h1 class="slide-title">The Protein Folding Problem: A Grand Challenge (Part 4)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Forces Driving Protein Folding:</strong></li>
<ul class="nested-list">
<li><strong>Hydrophobic Effect:</strong> Primary driving force. Nonpolar side chains cluster in the protein's interior to avoid water.</li>
<li><strong>Hydrogen Bonds:</strong> Stabilize secondary structures (α-helices and β-sheets) and specific interactions.</li>
<li><strong>Van der Waals Forces:</strong> Contribute to the tight packing of atoms in the protein core.</li>
<li><strong>Disulfide Bonds:</strong> Covalent links between cysteine residues that can stabilize the folded structure (especially in secreted proteins).</li>
<li><em>Visual: Illustration showing the different forces acting on a protein during folding (hydrophobic effect, H-bonds, Van der Waals, disulfide bonds).</em></li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-23" data-slide-number="23">
    <div class="slide-header">
        <span class="slide-number">23</span>
    </div>
    <h1 class="slide-title">Thermodynamics and the Energy Landscape</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Protein Folding: A Thermodynamic Imperative</strong></li>
<ul class="nested-list">
<li>Folding is driven by the tendency to achieve a state of minimal Gibbs Free Energy (ΔG &lt; 0).</li>
<li>The native state represents the most thermodynamically stable conformation.</li>
</ul>
<li><strong>The Energy Landscape: A Funnel Model</strong></li>
<ul class="nested-list">
<li><strong>Concept:</strong> Visualizes the folding process as a journey down a funnel.</li>
<li><strong>Funnel Shape:</strong> Represents decreasing free energy as the protein folds towards the native state.</li>
<li><strong>Roughness:</strong> The landscape isn't perfectly smooth; small "bumps" represent local energy minima (metastable intermediates).</li>
<li><strong>Visual:</strong> Show a diagram of the energy landscape funnel, labeling the unfolded state (high energy, many conformations) and the native state (low energy, single conformation) at the bottom.</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-24" data-slide-number="24">
    <div class="slide-header">
        <span class="slide-number">24</span>
    </div>
    <h1 class="slide-title">Thermodynamics and the Energy Landscape (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Minimizing Free Energy</strong></li>
<ul class="nested-list">
<li>Hydrophobic effect: Key driving force, sequestering hydrophobic residues inside the protein.</li>
<li>Hydrogen bonds: Stabilize secondary structures (α-helices and β-sheets) and tertiary structure.</li>
<li>Van der Waals forces: Contribute to the overall stability through close packing of atoms.</li>
<li>Conformational Entropy: Unfolded state has high entropy, folding decreases entropy, but favorable enthalpy changes overcome this entropic penalty.</li>
<li><strong>Visual:</strong> show how hydrophobic amino acids aggregate in the interior, and polar/charged amino acids reside on the exterior of the protein molecule.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-25" data-slide-number="25">
    <div class="slide-header">
        <span class="slide-number">25</span>
    </div>
    <h1 class="slide-title">Thermodynamics and the Energy Landscape (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Chaperones: Folding Assistants</strong></li>
<ul class="nested-list">
<li><strong>Role:</strong> Proteins that assist in the proper folding of other proteins.</li>
<li><strong>Mechanism:</strong> Prevent aggregation by binding to unfolded or partially folded proteins.</li>
<li><strong>Examples:</strong></li>
<ul class="nested-list">
<li>Hsp70: Binds to hydrophobic regions, preventing aggregation.</li>
<li>Chaperonins (e.g., GroEL/GroES): Provide a protected environment for folding.</li>
</ul>
<li><strong>Visual:</strong> A simple diagram of a chaperone protein binding to a misfolded protein, preventing aggregation.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-26" data-slide-number="26">
    <div class="slide-header">
        <span class="slide-number">26</span>
    </div>
    <h1 class="slide-title">Summary: Putting It All Together</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Proteins: The Molecular Workhorses</strong></li>
<ul class="nested-list">
<li>Complex molecules with a hierarchical structure, vital for all life processes.</li>
<li>Example: Enzymes catalyze reactions, antibodies provide immunity, structural proteins build tissues.</li>
</ul>
<li><strong>Sequence Defines Structure &amp; Function</strong></li>
<ul class="nested-list">
<li>The amino acid sequence (primary structure) dictates the protein's 3D structure and, consequently, its function.</li>
<li>Think of it as a recipe: the order of ingredients (amino acids) determines the final dish (protein).</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-27" data-slide-number="27">
    <div class="slide-header">
        <span class="slide-number">27</span>
    </div>
    <h1 class="slide-title">Summary: Putting It All Together (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Folding: A Symphony of Forces</strong></li>
<ul class="nested-list">
<li>Protein folding is a complex process driven by:</li>
<ul class="nested-list">
<li><strong>Hydrophobic effect:</strong> Nonpolar amino acids cluster inside.</li>
<li><strong>Hydrogen bonds:</strong> Stabilize secondary structures (alpha-helices, beta-sheets).</li>
<li><strong>Disulfide bonds:</strong> Covalent links adding stability.</li>
</ul>
<li>Visual: Show a protein folding pathway, highlighting the energy funnel.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-28" data-slide-number="28">
    <div class="slide-header">
        <span class="slide-number">28</span>
    </div>
    <h1 class="slide-title">Summary: Putting It All Together (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Structure = Understanding Biology</strong></li>
<ul class="nested-list">
<li>Knowing a protein's structure reveals its mechanism of action.</li>
<li>Example: Understanding enzyme active sites allows for drug design.</li>
</ul>
<li><strong>The Future is Bright: Protein Research &amp; Applications</strong></li>
<ul class="nested-list">
<li><strong>Protein Engineering:</strong> Designing proteins with new functions.</li>
<li><strong>Structural Biology:</strong> Unraveling protein structures through X-ray crystallography, cryo-EM, and NMR.</li>
<li><strong>Drug Discovery:</strong> Targeting proteins for therapeutic intervention.</li>
<li>Visual: A montage of protein research applications, like a scientist studying a protein structure on a computer, a new drug in development, etc.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

    </div>

    <div class="controls">
        <button class="control-button" id="prev-button" title="Previous slide">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="control-button" id="next-button" title="Next slide">
            <i class="fas fa-chevron-right"></i>
        </button>
        <button class="control-button" id="fullscreen-button" title="Toggle fullscreen">
            <i class="fas fa-expand"></i>
        </button>
        <button class="control-button" id="zoom-in-button" title="Zoom in">
            <i class="fas fa-search-plus"></i>
        </button>
        <button class="control-button" id="zoom-out-button" title="Zoom out">
            <i class="fas fa-search-minus"></i>
        </button>
        <button class="control-button" id="reset-zoom-button" title="Reset zoom">
            <i class="fas fa-sync-alt"></i>
        </button>
    </div>

    <div class="theme-switcher">
        <button class="theme-button" id="theme-toggle">Change Theme</button>
    </div>

    <script>
        // Slide navigation functionality
        const slides = document.querySelectorAll('.slide');
        const totalSlides = 28;
        let currentSlide = 0;

        // Progress bar update
        function updateProgress() {
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                bar.style.width = `${((currentSlide + 1) / totalSlides) * 100}%`;
            });
        }

        // Show the current slide with elegant transitions
        function showSlide(index) {
            // Get previous slide index
            const prevIndex = currentSlide;

            // Hide all slides and remove transition classes
            slides.forEach(slide => {
                slide.classList.remove('active', 'prev', 'next');
            });

            // Add appropriate transition classes
            if (prevIndex !== index && prevIndex >= 0 && prevIndex < slides.length) {
                if (prevIndex < index) {
                    slides[prevIndex].classList.add('prev');
                } else {
                    slides[prevIndex].classList.add('next');
                }
            }

            // Show the current slide
            slides[index].classList.add('active');

            // Update progress
            updateProgress();

            // Reset zoom and auto-fit content for the new slide
            resetZoom();
            setTimeout(autoFitContent, 100);

            // Add animation delay to content elements
            const contentElements = slides[index].querySelectorAll('.content > *');
            contentElements.forEach((element, i) => {
                element.style.setProperty('--animation-order', i);
            });

            // Update document title with slide title
            const slideTitle = slides[index].querySelector('.slide-title').textContent;
            document.title = slideTitle + ' | Presentation';
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        function prevSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        // Theme switching functionality with elegant themes
        const themes = ['elegant-dark', 'elegant-light', 'royal-purple', 'ocean-blue', 'sunset'];
        let currentTheme = 0;

        function switchTheme() {
            currentTheme = (currentTheme + 1) % themes.length;
            document.body.setAttribute('data-theme', themes[currentTheme]);

            // Update CSS variables based on theme
            const themeColors = {
                "elegant-dark": {
                    "background": "linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
                    "text": "#ffffff",
                    "accent": "#0f3460",
                    "secondary": "#e94560",
                    "subtle_accent": "rgba(15, 52, 96, 0.2)",
                    "highlight": "#f1c40f",
                    "muted": "rgba(255, 255, 255, 0.7)",
                    "shadow": "rgba(0, 0, 0, 0.2)"
                },
                "elegant-light": {
                    "background": "linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%)",
                    "text": "#333333",
                    "accent": "#3498db",
                    "secondary": "#2ecc71",
                    "subtle_accent": "rgba(52, 152, 219, 0.1)",
                    "highlight": "#e74c3c",
                    "muted": "rgba(0, 0, 0, 0.6)",
                    "shadow": "rgba(0, 0, 0, 0.1)"
                },
                "royal-purple": {
                    "background": "linear-gradient(135deg, #2c2157 0%, #1e1646 100%)",
                    "text": "#f5f5f5",
                    "accent": "#8a4fff",
                    "secondary": "#ff7eb6",
                    "subtle_accent": "rgba(138, 79, 255, 0.2)",
                    "highlight": "#ffcf5c",
                    "muted": "rgba(245, 245, 245, 0.7)",
                    "shadow": "rgba(0, 0, 0, 0.3)"
                },
                "ocean-blue": {
                    "background": "linear-gradient(135deg, #f0f5f9 0%, #e4f0f5 100%)",
                    "text": "#1e2022",
                    "accent": "#1e5f74",
                    "secondary": "#133b5c",
                    "subtle_accent": "rgba(30, 95, 116, 0.1)",
                    "highlight": "#fcdab7",
                    "muted": "rgba(30, 32, 34, 0.7)",
                    "shadow": "rgba(19, 59, 92, 0.1)"
                },
                "sunset": {
                    "background": "linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
                    "text": "#ffffff",
                    "accent": "#ff9a3c",
                    "secondary": "#ff6a88",
                    "subtle_accent": "rgba(255, 154, 60, 0.2)",
                    "highlight": "#ffde7d",
                    "muted": "rgba(255, 255, 255, 0.7)",
                    "shadow": "rgba(0, 0, 0, 0.25)"
                }
            };

            // Apply theme with smooth transition
            document.body.style.transition = 'background 0.5s ease';

            // Get the selected theme
            const selectedTheme = themeColors[themes[currentTheme]];

            // Set all CSS variables
            document.documentElement.style.setProperty('--background', selectedTheme.background);
            document.documentElement.style.setProperty('--text-color', selectedTheme.text);
            document.documentElement.style.setProperty('--accent-color', selectedTheme.accent);
            document.documentElement.style.setProperty('--secondary-color', selectedTheme.secondary);
            document.documentElement.style.setProperty('--subtle-accent', selectedTheme.subtle_accent);
            document.documentElement.style.setProperty('--highlight-color', selectedTheme.highlight);
            document.documentElement.style.setProperty('--muted-text', selectedTheme.muted);
            document.documentElement.style.setProperty('--shadow-color', selectedTheme.shadow);

            // Format theme name for display
            const themeName = themes[currentTheme].split('-').map(word =>
                word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ');

            document.getElementById('theme-toggle').textContent = themeName;

            // Add a visual feedback for theme change
            const feedback = document.createElement('div');
            feedback.className = 'theme-change-feedback';
            feedback.textContent = 'Theme: ' + themeName;
            feedback.style.position = 'fixed';
            feedback.style.top = '50%';
            feedback.style.left = '50%';
            feedback.style.transform = 'translate(-50%, -50%)';
            feedback.style.background = selectedTheme.accent;
            feedback.style.color = selectedTheme.text;
            feedback.style.padding = '1rem 2rem';
            feedback.style.borderRadius = '30px';
            feedback.style.boxShadow = '0 4px 20px ' + selectedTheme.shadow;
            feedback.style.zIndex = '1000';
            feedback.style.opacity = '0';
            feedback.style.transition = 'opacity 0.3s ease';

            document.body.appendChild(feedback);

            // Show and hide feedback
            setTimeout(() => { feedback.style.opacity = '1'; }, 50);
            setTimeout(() => {
                feedback.style.opacity = '0';
                setTimeout(() => { document.body.removeChild(feedback); }, 300);
            }, 1500);
        }

        // Fullscreen functionality
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log(`Error attempting to enable fullscreen: ${err.message}`);
                });
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        }

        // Zoom functionality
        let currentZoom = 1;
        const MIN_ZOOM = 0.5;
        const MAX_ZOOM = 2;
        const ZOOM_STEP = 0.1;

        function setZoom(zoomLevel) {
            currentZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, zoomLevel));
            document.querySelectorAll('.content').forEach(content => {
                content.style.transform = `scale(${currentZoom})`;
                content.style.transformOrigin = 'top left';
            });
        }

        function zoomIn() {
            setZoom(currentZoom + ZOOM_STEP);
        }

        function zoomOut() {
            setZoom(currentZoom - ZOOM_STEP);
        }

        function resetZoom() {
            setZoom(1);
        }

        // Auto-fit content if it overflows
        function autoFitContent() {
            const activeSlide = document.querySelector('.slide.active');
            if (!activeSlide) return;

            const content = activeSlide.querySelector('.content');
            const slideHeight = activeSlide.clientHeight;
            const titleHeight = activeSlide.querySelector('.slide-title').offsetHeight;
            const footerHeight = activeSlide.querySelector('.slide-footer').offsetHeight;
            const availableHeight = slideHeight - titleHeight - footerHeight - 120; // 120px for padding

            if (content.scrollHeight > availableHeight) {
                // Content is too tall, calculate zoom level to fit
                const zoomFactor = availableHeight / content.scrollHeight;
                if (zoomFactor < 1) {
                    setZoom(Math.max(MIN_ZOOM, zoomFactor));
                }
            }
        }

        // Initialize presentation
        function initPresentation() {
            // Show first slide
            showSlide(currentSlide);

            // Add event listeners
            document.getElementById('next-button').addEventListener('click', nextSlide);
            document.getElementById('prev-button').addEventListener('click', prevSlide);
            document.getElementById('fullscreen-button').addEventListener('click', toggleFullscreen);
            document.getElementById('theme-toggle').addEventListener('click', switchTheme);
            document.getElementById('zoom-in-button').addEventListener('click', zoomIn);
            document.getElementById('zoom-out-button').addEventListener('click', zoomOut);
            document.getElementById('reset-zoom-button').addEventListener('click', resetZoom);

            // Add keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowRight' || e.key === ' ' || e.key === 'n') {
                    nextSlide();
                } else if (e.key === 'ArrowLeft' || e.key === 'p') {
                    prevSlide();
                } else if (e.key === 'f') {
                    toggleFullscreen();
                } else if (e.key === 't') {
                    switchTheme();
                } else if (e.key === '+' || e.key === '=') {
                    zoomIn();
                } else if (e.key === '-') {
                    zoomOut();
                } else if (e.key === '0') {
                    resetZoom();
                }
            });

            // Animate content elements with delay
            const contentElements = document.querySelectorAll('.content > *');
            contentElements.forEach((element, index) => {
                element.classList.add('fade-in');
                element.style.animationDelay = `${index * 0.2}s`;
            });

            // Set initial theme
            switchTheme(); // Apply the first theme

            // Initialize syntax highlighting for code blocks
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });

            // Auto-fit content if needed
            setTimeout(autoFitContent, 500);

            // Add resize listener to handle window size changes
            window.addEventListener('resize', function() {
                resetZoom();
                setTimeout(autoFitContent, 200);
            });
        }

        // Run initialization when DOM is loaded
        document.addEventListener('DOMContentLoaded', initPresentation);
    </script>
</body>
</html>
