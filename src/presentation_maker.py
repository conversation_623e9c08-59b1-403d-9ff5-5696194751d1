"""
Presentation Maker using PocketFlow.
"""
from typing import Dict, Any, Optional
from pocketflow import Flow

from src.nodes.material_processor import MaterialProcessor
from src.nodes.outline_generator import OutlineGenerator
from src.nodes.slide_creator import SlideCreator
from src.nodes.image_finder import ImageFinder
from src.nodes.presentation_builder import PresentationBuilder
from src.utils.llm_wrapper import LLMWrapper
from src.utils.presentation_utils import PresentationFormat, PresentationUtils

class PresentationMaker:
    """
    Agent for creating presentations from input material.
    """

    def __init__(self,
                model: str = "gemini-2.0-flash",
                api_key: Optional[str] = None,
                provider: str = "gemini",
                format_type: str = PresentationFormat.MARKDOWN):
        """
        Initialize the presentation maker.

        Args:
            model: The LLM model to use
            api_key: API key for the LLM provider
            provider: The LLM provider to use (gemini or openai)
            format_type: The format to use for the presentation
        """
        self.llm_wrapper = LLMWrapper(model=model, api_key=api_key, provider=provider)
        self.format_type = format_type

        # Create the nodes
        self.material_processor = MaterialProcessor(self.llm_wrapper)
        self.outline_generator = OutlineGenerator(self.llm_wrapper)
        self.slide_creator = SlideCreator(self.llm_wrapper)
        self.image_finder = ImageFinder(self.llm_wrapper)
        self.presentation_builder = PresentationBuilder(format_type=format_type)

        # Create the flow with connections
        # In PocketFlow, we use the >> operator to connect nodes
        self.material_processor >> self.outline_generator >> self.slide_creator >> self.presentation_builder

        # with image finder
        # self.material_processor >> self.outline_generator >> self.slide_creator >> self.image_finder >> self.presentation_builder

        # Create the flow with the start node
        self.flow = Flow(start=self.material_processor)

    def create_presentation(self, material: str, output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a presentation from the input material.

        Args:
            material: The input material to create a presentation from
            output_file: Optional file path to save the presentation

        Returns:
            Dictionary with the presentation details
        """
        # For testing with mock implementation
        if self.llm_wrapper.use_mock:
            # Get the outline
            outline = self.get_outline(material)

            # Create mock slides
            slides = []
            for slide_outline in outline["slides"]:
                slide_content = "- " + "\n- ".join(slide_outline["key_points"])

                # Add a mock image to each slide
                image_path = "images/mock_image.jpg"
                slide_content = f"\n\n![Illustration for {slide_outline['title']}]({image_path})\n\n" + slide_content

                slides.append({
                    "title": slide_outline["title"],
                    "content": slide_content,
                    "image_path": image_path
                })

            # Create the presentation
            title = outline["title"]
            if self.format_type == PresentationFormat.MARKDOWN:
                presentation_content = PresentationUtils.create_presentation_markdown(slides, title)
            else:
                presentation_content = PresentationUtils.create_presentation_html(slides, title)

            # Create the result
            result = {
                "outline": outline,
                "slides": slides,
                "presentation": {
                    "title": title,
                    "content": presentation_content,
                    "format": self.format_type
                }
            }

            # Save the presentation if output file is provided
            if output_file:
                saved_file = PresentationUtils.save_presentation(
                    content=presentation_content,
                    filename=output_file,
                    format_type=self.format_type
                )
                result["saved_file"] = saved_file

            return result

        # Initialize the shared state with the input material
        shared = {"material": material}
        # Run the flow
        self.flow.run(shared)
        
        # Check for errors
        if "error" in shared:
            return {"error": shared["error"]}

        # Save the presentation if output file is provided
        if output_file and "presentation" in shared:
            presentation = shared["presentation"]
            saved_file = PresentationUtils.save_presentation(
                content=presentation["content"],
                filename=output_file,
                format_type=self.format_type
            )
            shared["saved_file"] = saved_file

        return shared

    def get_outline(self, material: str) -> Dict[str, Any]:
        """
        Generate just the outline from the input material.

        Args:
            material: The input material

        Returns:
            Dictionary with the outline
        """
        # For testing with mock implementation or when API calls fail
        if self.llm_wrapper.use_mock:
            return {
                "title": "Artificial Intelligence: Past, Present, and Future",
                "slides": [
                    {
                        "title": "Introduction to AI",
                        "key_points": [
                            "Definition of Artificial Intelligence",
                            "Brief history since the 1950s",
                            "Evolution from rule-based systems to machine learning"
                        ]
                    },
                    {
                        "title": "Current AI Landscape",
                        "key_points": [
                            "Deep learning breakthroughs",
                            "Computer Vision and NLP advances",
                            "Recent milestones (AlexNet, AlphaGo, GPT-3)"
                        ]
                    },
                    {
                        "title": "Future of AI",
                        "key_points": [
                            "Opportunities in healthcare, climate change, and science",
                            "Ethical challenges and concerns",
                            "Prospects for Artificial General Intelligence"
                        ]
                    },
                    {
                        "title": "Conclusion",
                        "key_points": [
                            "Summary of AI's transformative potential",
                            "Importance of responsible development",
                            "Call for collaboration across disciplines"
                        ]
                    }
                ]
            }

        try:
            # Process the material directly
            system_prompt = """
            You are an expert at analyzing and extracting key information from text.
            Your task is to process the provided material and identify the most important
            concepts, facts, and ideas that should be included in a presentation.
            """

            prompt = f"""
            Please analyze the following material and extract the key information that should be
            included in a presentation. Focus on the main ideas, important facts, and concepts.

            MATERIAL:
            {material}

            Please provide a structured analysis with the following:
            1. Main topic and purpose
            2. Key concepts and ideas
            3. Important facts and data
            4. Potential audience for this material
            5. Suggested tone and style for the presentation
            """

            processed_material = self.llm_wrapper.generate(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=0.3,
                max_tokens=2000
            )

            # Generate the outline
            outline_schema = {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "slides": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "title": {"type": "string"},
                                "key_points": {
                                    "type": "array",
                                    "items": {"type": "string"}
                                }
                            },
                            "required": ["title", "key_points"]
                        }
                    }
                },
                "required": ["title", "slides"]
            }

            system_prompt = """
            You are an expert presentation designer who creates clear, engaging, and well-structured
            presentation outlines. Your outlines should be comprehensive yet concise, covering all
            important information in a logical flow.
            """

            prompt = f"""
            Based on the following processed material, create a detailed outline for a presentation.
            The outline should include a compelling title and a series of slides with clear titles
            and key points to cover in each slide.

            PROCESSED MATERIAL:
            {processed_material}

            Create an outline that:
            1. Has a clear and engaging title
            2. Includes an introduction slide
            3. Presents information in a logical sequence
            4. Covers all key points from the material
            5. Includes a conclusion/summary slide

            For each slide, provide:
            - A clear, concise title
            - 3-5 key points to cover in bullet form
            """

            outline = self.llm_wrapper.generate_structured(
                prompt=prompt,
                output_schema=outline_schema,
                system_prompt=system_prompt,
                temperature=0.4
            )

            if isinstance(outline, dict) and "error" in outline:
                print(f"Error generating outline: {outline['error']}")
                if "raw_response" in outline:
                    print(f"Raw response: {outline['raw_response']}")
                # Fall back to mock implementation
                if not self.llm_wrapper.use_mock:
                    self.llm_wrapper.use_mock = True
                    return self.get_outline(material)
                else:
                    return {"error": outline["error"]}

            return outline

        except Exception as e:
            print(f"Error in get_outline: {str(e)}")
            # Fall back to mock implementation
            if not self.llm_wrapper.use_mock:
                self.llm_wrapper.use_mock = True
                return self.get_outline(material)
            else:
                return {"error": f"Failed to generate outline: {str(e)}"}
