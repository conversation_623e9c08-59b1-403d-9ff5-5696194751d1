
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quantum Support Vector Machines: A Quantum Leap in Classification?</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500&display=swap');

        :root {
            --background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            --text-color: #ffffff;
            --accent-color: #0f3460;
            --secondary-color: #e94560;

            /* Additional elegant color variables */
            --subtle-accent: rgba(15, 52, 96, 0.2);
            --highlight-color: #f1c40f;
            --muted-text: rgba(255, 255, 255, 0.7);
            --shadow-color: rgba(0, 0, 0, 0.2);

            /* Typography variables */
            --heading-font: 'Cormorant Garamond', 'Playfair Display', serif;
            --body-font: 'Poppins', sans-serif;
            --code-font: 'Fira Code', monospace;

            /* Spacing variables */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 2rem;
            --spacing-xl: 4rem;

            /* Animation variables */
            --transition-fast: 0.3s ease;
            --transition-medium: 0.5s ease;
            --transition-slow: 0.8s cubic-bezier(0.77, 0, 0.175, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--body-font);
            background: var(--background);
            color: var(--text-color);
            overflow: hidden;
            transition: background var(--transition-medium);
            line-height: 1.6;
            font-weight: 300;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .presentation-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: var(--spacing-xl) var(--spacing-xl);
            opacity: 0;
            transform: translateY(50px);
            transition: all var(--transition-slow);
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--accent-color) transparent;
            background-size: cover;
            background-position: center;
        }

        /* Custom scrollbar styling */
        .slide::-webkit-scrollbar {
            width: 6px;
        }

        .slide::-webkit-scrollbar-track {
            background: transparent;
        }

        .slide::-webkit-scrollbar-thumb {
            background-color: var(--accent-color);
            border-radius: 3px;
        }

        .slide.active {
            opacity: 1;
            transform: translateY(0);
            animation: slideFadeIn var(--transition-slow) forwards;
        }

        @keyframes slideFadeIn {
            0% { opacity: 0; transform: translateY(30px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--subtle-accent) 0%, transparent 50%);
            opacity: 0.5;
            z-index: -1;
        }

        .slide-header {
            position: absolute;
            top: var(--spacing-lg);
            right: var(--spacing-lg);
            z-index: 10;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .slide-number {
            font-size: 1rem;
            opacity: 0.6;
            font-weight: 300;
            font-family: var(--heading-font);
            letter-spacing: 1px;
            background: var(--subtle-accent);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: 20px;
            min-width: 2.5rem;
            text-align: center;
        }

        .slide-title {
            font-family: var(--heading-font);
            font-size: 3.8rem;
            font-weight: 600;
            margin-bottom: var(--spacing-lg);
            color: var(--accent-color);
            position: relative;
            display: inline-block;
            max-width: 90%;
            word-wrap: break-word;
            line-height: 1.2;
            letter-spacing: -0.5px;
            text-shadow: 1px 1px 2px var(--shadow-color);
        }

        .slide-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 100px;
            height: 3px;
            background: linear-gradient(to right, var(--accent-color), transparent);
            transform-origin: left;
            animation: expandWidth 1.5s ease forwards;
        }

        @keyframes expandWidth {
            0% { width: 0; opacity: 0; }
            100% { width: 100px; opacity: 1; }
        }

        .content {
            font-size: 1.25rem;
            line-height: 1.6;
            max-width: 900px;
            width: 100%;
            overflow-wrap: break-word;
            word-wrap: break-word;
            hyphens: auto;
            margin-bottom: var(--spacing-lg);
            position: relative;
            z-index: 1;
        }

        /* Add elegant styling to content elements */
        .elegant-paragraph {
            margin-bottom: var(--spacing-md);
            font-weight: 300;
            color: var(--text-color);
            text-align: justify;
            text-justify: inter-word;
        }

        .elegant-paragraph:first-letter {
            font-size: 1.5em;
            font-weight: 500;
            color: var(--accent-color);
            font-family: var(--heading-font);
        }

        .elegant-list {
            margin: var(--spacing-md) 0;
            padding-left: var(--spacing-lg);
        }

        .elegant-list li {
            margin-bottom: var(--spacing-sm);
            position: relative;
        }

        .elegant-list li::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 10px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--accent-color);
        }

        .nested-list {
            margin-top: var(--spacing-sm);
            margin-left: var(--spacing-md);
        }

        .elegant-quote {
            border-left: 3px solid var(--accent-color);
            padding: var(--spacing-md) var(--spacing-lg);
            margin: var(--spacing-md) 0;
            background: var(--subtle-accent);
            border-radius: 0 8px 8px 0;
            font-style: italic;
            position: relative;
        }

        .elegant-quote::before {
            content: '"';
            font-family: var(--heading-font);
            font-size: 4rem;
            position: absolute;
            top: -20px;
            left: 10px;
            color: var(--accent-color);
            opacity: 0.2;
        }

        .elegant-hr {
            border: none;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--accent-color), transparent);
            margin: var(--spacing-lg) 0;
        }

        .elegant-hr.thick {
            height: 3px;
        }

        .elegant-image {
            margin: var(--spacing-md) 0;
            position: relative;
            display: inline-block;
            max-width: 100%;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px var(--shadow-color);
        }

        .elegant-image img {
            max-width: 100%;
            display: block;
            transition: transform var(--transition-medium);
        }

        .elegant-image:hover img {
            transform: scale(1.02);
        }

        .elegant-image figcaption {
            padding: var(--spacing-sm);
            text-align: center;
            font-style: italic;
            font-size: 0.9rem;
            color: var(--muted-text);
        }

        .table-container {
            overflow-x: auto;
            margin: var(--spacing-md) 0;
            border-radius: 8px;
            box-shadow: 0 4px 12px var(--shadow-color);
        }

        .elegant-table {
            width: 100%;
            border-collapse: collapse;
            overflow: hidden;
        }

        .elegant-table th, .elegant-table td {
            padding: var(--spacing-sm) var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--subtle-accent);
        }

        .elegant-table th {
            background: var(--subtle-accent);
            font-weight: 600;
            color: var(--accent-color);
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 1px;
        }

        .elegant-table tr:last-child td {
            border-bottom: none;
        }

        .elegant-table tr:hover {
            background: var(--subtle-accent);
        }

        .code-block {
            margin: var(--spacing-md) 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px var(--shadow-color);
            position: relative;
        }

        .code-block code {
            font-family: var(--code-font);
            font-size: 0.9rem;
            line-height: 1.5;
            display: block;
            padding: var(--spacing-md);
            overflow-x: auto;
        }

        .line-number {
            display: inline-block;
            width: 2rem;
            text-align: right;
            color: var(--muted-text);
            margin-right: var(--spacing-sm);
            user-select: none;
        }

        .inline-code {
            font-family: var(--code-font);
            background: var(--subtle-accent);
            padding: 0.1em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .task-list {
            list-style: none;
            padding-left: var(--spacing-md);
            margin: var(--spacing-md) 0;
        }

        .task-item {
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
        }

        .checkbox {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 50%;
            margin-right: var(--spacing-sm);
            background: var(--subtle-accent);
            color: var(--accent-color);
            font-size: 0.8rem;
        }

        .task-item.completed {
            text-decoration: line-through;
            opacity: 0.7;
        }

        /* Navigation hints for multi-part slides */
        .slide-part-indicator {
            position: absolute;
            bottom: 4rem;
            right: 2rem;
            background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 30px;
            font-size: 0.8rem;
            opacity: 0.8;
            transition: all var(--transition-fast);
            box-shadow: 0 4px 12px var(--shadow-color);
            transform: translateY(0);
        }

        .slide-part-indicator:hover {
            opacity: 1;
            transform: translateY(-3px);
            box-shadow: 0 6px 16px var(--shadow-color);
        }

        /* Markdown-rendered HTML elements styling */
        .content h1, .content h2, .content h3, .content h4, .content h5, .content h6 {
            margin-top: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            color: var(--secondary-color);
            font-family: var(--heading-font);
            font-weight: 600;
            line-height: 1.2;
            letter-spacing: -0.5px;
            position: relative;
            display: inline-block;
        }

        .content h1::after, .content h2::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(to right, var(--accent-color), transparent);
        }

        .content h1 {
            font-size: 2.5rem;
            margin-top: var(--spacing-xl);
        }

        .content h2 {
            font-size: 2rem;
            color: var(--accent-color);
        }

        .content h3 {
            font-size: 1.75rem;
            font-weight: 500;
        }

        .content h4 {
            font-size: 1.5rem;
            font-weight: 500;
            color: var(--accent-color);
            opacity: 0.9;
        }

        .content h5 {
            font-size: 1.25rem;
            font-weight: 500;
            font-style: italic;
        }

        .content h6 {
            font-size: 1rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .content p {
            margin-bottom: var(--spacing-md);
            line-height: 1.7;
        }

        .content a {
            color: var(--accent-color);
            text-decoration: none;
            border-bottom: 1px dotted var(--accent-color);
            transition: all var(--transition-fast);
            position: relative;
            display: inline-block;
            padding: 0 2px;
        }

        .content a:hover {
            border-bottom: 1px solid var(--accent-color);
            background: var(--subtle-accent);
            border-radius: 3px;
        }

        .content a::after {
            content: '↗';
            font-size: 0.8em;
            position: relative;
            top: -0.5em;
            opacity: 0;
            margin-left: 2px;
            transition: opacity var(--transition-fast);
        }

        .content a:hover::after {
            opacity: 1;
        }

        .content strong {
            font-weight: 600;
            color: var(--accent-color);
            padding: 0 2px;
        }

        .content em {
            font-style: italic;
            color: var(--secondary-color);
        }

        .content del {
            text-decoration: line-through;
            opacity: 0.7;
        }

        /* Lists are now styled with .elegant-list class */

        /* Images are now styled with .elegant-image class */

        /* Blockquotes are now styled with .elegant-quote class */

        /* Code blocks are now styled with .code-block class */

        /* Tables are now styled with .elegant-table class */

        /* Add animation for content elements */
        .content > * {
            animation: fadeSlideUp 0.8s ease forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        @keyframes fadeSlideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-footer {
            position: absolute;
            bottom: var(--spacing-lg);
            left: 0;
            width: 100%;
            padding: 0 var(--spacing-xl);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .progress-indicator {
            width: 100%;
            height: 4px;
            background: var(--subtle-accent);
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(to right, var(--accent-color), var(--secondary-color));
            transition: width var(--transition-medium);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                rgba(255,255,255,0) 0%,
                rgba(255,255,255,0.3) 50%,
                rgba(255,255,255,0) 100%
            );
            animation: shimmer 2s infinite;
            transform: translateX(-100%);
        }

        @keyframes shimmer {
            100% { transform: translateX(100%); }
        }

        .controls {
            position: fixed;
            bottom: var(--spacing-lg);
            right: var(--spacing-lg);
            z-index: 100;
            display: flex;
            gap: var(--spacing-xs);
            background: rgba(0, 0, 0, 0.1);
            padding: var(--spacing-xs);
            border-radius: 50px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px var(--shadow-color);
        }

        .control-button {
            background: var(--subtle-accent);
            color: var(--text-color);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: 0.9rem;
        }

        .control-button:hover {
            background: var(--accent-color);
            transform: scale(1.1);
            color: white;
            box-shadow: 0 2px 10px var(--shadow-color);
        }

        .control-button:active {
            transform: scale(0.95);
        }

        .theme-switcher {
            position: fixed;
            top: var(--spacing-lg);
            left: var(--spacing-lg);
            z-index: 100;
        }

        .theme-button {
            background: var(--subtle-accent);
            color: var(--text-color);
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 30px;
            cursor: pointer;
            transition: all var(--transition-fast);
            backdrop-filter: blur(10px);
            font-size: 0.9rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 12px var(--shadow-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .theme-button::before {
            content: '🎨';
            font-size: 1rem;
        }

        .theme-button:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px var(--shadow-color);
        }

        .theme-button:active {
            transform: translateY(0);
        }

        /* Animations for content */
        .fade-in {
            animation: fadeIn 1s ease forwards;
            animation-delay: calc(var(--animation-order, 0) * 0.1s);
            opacity: 0;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Elegant transitions between slides */
        .slide.prev {
            transform: translateX(-50px);
            opacity: 0;
            transition: all var(--transition-slow);
        }

        .slide.next {
            transform: translateX(50px);
            opacity: 0;
            transition: all var(--transition-slow);
        }

        /* Elegant background patterns */
        .slide::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background-image: radial-gradient(circle, var(--subtle-accent) 10%, transparent 10.5%);
            background-size: 20px 20px;
            background-position: 0 0;
            opacity: 0.3;
            z-index: -1;
            pointer-events: none;
        }

        /* Responsive adjustments with CSS variables */
        @media (max-width: 1200px) {
            :root {
                --spacing-xl: 3rem;
                --spacing-lg: 1.5rem;
            }

            .slide {
                padding: var(--spacing-xl);
            }

            .slide-title {
                font-size: 3.2rem;
                max-width: 95%;
            }

            .content {
                font-size: 1.15rem;
            }

            .elegant-paragraph:first-letter {
                font-size: 1.3em;
            }
        }

        @media (max-width: 992px) {
            :root {
                --spacing-xl: 2.5rem;
                --spacing-lg: 1.25rem;
                --spacing-md: 0.75rem;
            }

            .slide {
                padding: var(--spacing-xl);
            }

            .slide-title {
                font-size: 2.8rem;
            }

            .content {
                font-size: 1.1rem;
                max-width: 100%;
            }

            .slide-footer {
                padding: 0 var(--spacing-xl);
            }

            .elegant-quote::before {
                font-size: 3rem;
            }

            .slide::after {
                width: 200px;
                height: 200px;
            }
        }

        @media (max-width: 768px) {
            :root {
                --spacing-xl: 2rem;
                --spacing-lg: 1rem;
                --spacing-md: 0.5rem;
            }

            .slide {
                padding: var(--spacing-xl);
            }

            .slide-title {
                font-size: 2.5rem;
                margin-bottom: var(--spacing-md);
            }

            .content {
                font-size: 1rem;
            }

            .slide-footer {
                padding: 0 var(--spacing-xl);
                bottom: var(--spacing-lg);
            }

            .controls {
                bottom: var(--spacing-lg);
                right: var(--spacing-lg);
            }

            .theme-switcher {
                top: var(--spacing-lg);
                left: var(--spacing-lg);
            }

            .elegant-image figcaption {
                font-size: 0.8rem;
            }

            .slide::after {
                width: 150px;
                height: 150px;
                background-size: 15px 15px;
            }
        }

        @media (max-width: 576px) {
            :root {
                --spacing-xl: 1.5rem;
                --spacing-lg: 0.75rem;
                --spacing-md: 0.5rem;
                --spacing-sm: 0.25rem;
            }

            .slide {
                padding: var(--spacing-xl);
            }

            .slide-title {
                font-size: 2rem;
            }

            .content {
                font-size: 0.9rem;
            }

            .slide-header {
                top: var(--spacing-md);
                right: var(--spacing-md);
            }

            .controls {
                bottom: var(--spacing-md);
                right: var(--spacing-md);
                gap: 2px;
                padding: 4px;
            }

            .theme-switcher {
                top: var(--spacing-md);
                left: var(--spacing-md);
            }

            .control-button {
                width: 32px;
                height: 32px;
                font-size: 0.8rem;
            }

            .theme-button {
                font-size: 0.8rem;
                padding: 4px 8px;
            }

            .theme-button::before {
                font-size: 0.9rem;
            }

            .slide-part-indicator {
                bottom: var(--spacing-xl);
                right: var(--spacing-md);
                font-size: 0.7rem;
                padding: 4px 8px;
            }

            .slide::after {
                width: 100px;
                height: 100px;
                background-size: 10px 10px;
            }
        }

        /* Handle very small screens and orientation */
        @media (max-height: 600px), (max-width: 400px) {
            .slide-title {
                font-size: 1.8rem;
                margin-bottom: var(--spacing-sm);
            }

            .content {
                font-size: 0.85rem;
                line-height: 1.4;
            }

            .content h1 { font-size: 1.6rem; }
            .content h2 { font-size: 1.4rem; }
            .content h3 { font-size: 1.2rem; }
            .content h4 { font-size: 1rem; }
            .content h5 { font-size: 0.9rem; }
            .content h6 { font-size: 0.8rem; }

            .elegant-paragraph:first-letter {
                font-size: 1.1em;
            }

            .elegant-quote {
                padding: var(--spacing-sm);
            }

            .elegant-quote::before {
                font-size: 2rem;
                top: -10px;
            }

            .slide::after {
                display: none;
            }
        }

        /* Print styles for PDF export */
        @media print {
            .slide {
                page-break-after: always;
                opacity: 1 !important;
                transform: none !important;
                position: relative;
                height: auto;
                min-height: 100vh;
            }

            .controls, .theme-switcher, .slide-part-indicator {
                display: none !important;
            }

            body {
                background: white !important;
                color: black !important;
            }

            .slide-footer {
                position: relative;
                margin-top: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">

<div class="slide" id="slide-1" data-slide-number="1">
    <div class="slide-header">
        <span class="slide-number">1</span>
    </div>
    <h1 class="slide-title">Introduction: Unveiling Quantum Support Vector Machines</h1>
    <div class="content">
        
<h1 id="introduction-unveiling-quantum-support-vector-machines">Introduction: Unveiling Quantum Support Vector Machines</h1>
<ul class="elegant-list">
<li><strong>The Promise of Quantum Machine Learning:</strong> Exploring quantum algorithms for enhanced machine learning.</li>
<li><strong>Classical SVM Bottleneck:</strong></li>
<ul class="nested-list">
<li>Powerful classification tool, <em>but</em>...</li>
<li>Computational cost scales quadratically with data size for complex kernels.</li>
<li><strong>Visual:</strong> Image depicting the hyperplane in SVM</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-2" data-slide-number="2">
    <div class="slide-header">
        <span class="slide-number">2</span>
    </div>
    <h1 class="slide-title">Introduction: Unveiling Quantum Support Vector Machines...</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Enter Quantum Support Vector Machines (QSVMs):</strong></li>
<ul class="nested-list">
<li>Quantum-enhanced alternative to classical SVMs.</li>
<li>Leverages quantum mechanics to accelerate kernel computation.</li>
<li><strong>Visual:</strong> Image depicting how data is encoded to quantum state and then a quantum kernel function.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-3" data-slide-number="3">
    <div class="slide-header">
        <span class="slide-number">3</span>
    </div>
    <h1 class="slide-title">Introduction: Unveiling Quantum Support Vector Machines...</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Key Concepts Covered:</strong></li>
<ul class="nested-list">
<li>QSVM architecture &amp; workflow (Quantum Feature Map, Quantum Kernel)</li>
<li>Advantages: Potential Speedup, Better Feature Representation.</li>
<li>Limitations: NISQ era constraints, Scalability issues.</li>
<li>Applications: Where SVMs are used today (e.g., image recognition, bioinformatics).</li>
<li>Implementation: Qiskit, PennyLane, TensorFlow Quantum.</li>
</ul>
<li><strong>Presentation Goals:</strong> Provide a clear, concise overview of QSVMs.</li>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-4" data-slide-number="4">
    <div class="slide-header">
        <span class="slide-number">4</span>
    </div>
    <h1 class="slide-title">Introduction: Unveiling Quantum Support Vector Machines...</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Target Audience:</strong></li>
<ul class="nested-list">
<li>ML Practitioners</li>
<li>Quantum Computing Researchers</li>
<li>Data Scientists</li>
<li>Students</li>
<li><strong>Visual:</strong> Icons representing each group of the target audience.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-5" data-slide-number="5">
    <div class="slide-header">
        <span class="slide-number">5</span>
    </div>
    <h1 class="slide-title">Classical SVMs: A Quick Recap</h1>
    <div class="content">
        
<h3 id="classical-svms-a-quick-recap">Classical SVMs: A Quick Recap</h3>
<ul class="elegant-list">
<li><strong>Goal:</strong> Find the optimal hyperplane to separate data classes.</li>
<ul class="nested-list">
<li><em>Visual:</em> Illustration of a hyperplane separating two classes of data points.</li>
</ul>
<li><strong>Kernel Functions:</strong> Enable non-linear separation by mapping data to higher dimensions.</li>
<ul class="nested-list">
<li><em>Visual:</em> Diagram showing data being transformed from a lower dimension to a higher dimension where separation is possible. Example kernels: Linear, Polynomial, Radial Basis Function (RBF).</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-6" data-slide-number="6">
    <div class="slide-header">
        <span class="slide-number">6</span>
    </div>
    <h1 class="slide-title">Classical SVMs: A Quick Recap (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Computational Cost:</strong> Scales quadratically (O(n^2)) with the number of data points (n), especially with complex kernels.</li>
<ul class="nested-list">
<li><em>Visual:</em> Graph showing quadratic scaling of computational cost with increasing data points.</li>
<li><em>Example:</em> Doubling the dataset size roughly quadruples the computation time.</li>
</ul>
<li><strong>Major Bottleneck:</strong> This quadratic scaling is a significant limitation for large datasets.</li>
<ul class="nested-list">
<li><em>Visual:</em> A "bottleneck" illustration highlighting the computational cost issue, pointing towards QSVM as a potential solution.</li>
</ul>
<li><strong>Why This Matters for QSVMs:</strong> Classical SVM's computational bottleneck with kernel computation is what QSVMs aim to address.</li>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-7" data-slide-number="7">
    <div class="slide-header">
        <span class="slide-number">7</span>
    </div>
    <h1 class="slide-title">The Quantum Advantage: Motivation for QSVMs</h1>
    <div class="content">
        
<h3 id="the-quantum-advantage-motivation-for-qsvms">The Quantum Advantage: Motivation for QSVMs</h3>
<ul class="elegant-list">
<li><strong>Classical SVM Bottleneck:</strong> Kernel calculations scale quadratically with dataset size. Becomes computationally expensive for large datasets and complex kernels.</li>
<ul class="nested-list">
<li><em>Visual Suggestion: Plot showing quadratic scaling of classical SVM runtime vs. dataset size.</em></li>
</ul>
<li><strong>Quantum Promise:</strong> Quantum computers offer potential <strong>exponential speedups</strong> for certain linear algebra operations.</li>
<ul class="nested-list">
<li><em>Visual Suggestion: Simple graphic illustrating quantum speedup for a matrix operation.</em></li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-8" data-slide-number="8">
    <div class="slide-header">
        <span class="slide-number">8</span>
    </div>
    <h1 class="slide-title">The Quantum Advantage: Motivation for QSVMs (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>QSVM Key Idea:</strong> Leverage quantum computers to efficiently compute inner products in high-dimensional feature spaces.</li>
<ul class="nested-list">
<li>Data mapped to quantum states: x → |φ(x)⟩</li>
<li><strong>Quantum Kernel:</strong> K(x, x') = |⟨φ(x) | φ(x')⟩|&lt;sup&gt;2&lt;/sup&gt;</li>
<ul class="nested-list">
<li>Classically hard to compute, efficiently estimable on a quantum computer!</li>
</ul>
</ul>
<li><strong>Quantum Feature Maps:</strong> Exploit the structure of quantum circuits to create feature mappings that would be intractable classically.</li>
<ul class="nested-list">
<li><em>Visual Suggestion: Example of a parameterized quantum circuit acting as a feature map.</em></li>
</ul>
<li><strong>Motivation Recap:</strong> QSVMs aim to overcome the classical computational bottleneck in kernel methods by using quantum circuits to accelerate kernel calculations, potentially enabling more powerful and efficient machine learning models.</li>
<ul class="nested-list">
<li><em>Visual Suggestion: Image representing the hybrid quantum-classical nature of QSVM, with quantum kernel computation and classical optimization.</em></li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-9" data-slide-number="9">
    <div class="slide-header">
        <span class="slide-number">9</span>
    </div>
    <h1 class="slide-title">Quantum Feature Maps: Encoding Data into Quantum States</h1>
    <div class="content">
        <p class="elegant-paragraph">
```
markdown</p>

<h3 id="quantum-feature-maps-encoding-data-into-quantum-states">Quantum Feature Maps: Encoding Data into Quantum States</h3>
<ul class="elegant-list">
<li><strong>The Core Idea:</strong> Encode classical data (x) into quantum states |φ(x)⟩. This process is called a <strong>quantum feature map.</strong></li>
<li><strong>Parameterized Quantum Circuits:</strong></li>
<ul class="nested-list">
<li>Quantum circuits with adjustable parameters implement the feature map.</li>
<li>By tuning these parameters, we can learn complex data representations.</li>
<li>Example: Repeated application of single-qubit rotations and entangling gates.</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-10" data-slide-number="10">
    <div class="slide-header">
        <span class="slide-number">10</span>
    </div>
    <h1 class="slide-title">Quantum Feature Maps: Encoding Data into Quantum States...</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>High-Dimensional Hilbert Space:</strong></li>
<ul class="nested-list">
<li>Quantum feature maps project data into a high-dimensional Hilbert space.</li>
<li>This allows for capturing complex, non-linear relationships that are difficult to model classically.</li>
<li>Imagine trying to separate two intertwined spirals on a 2D plane; in a higher dimension, this separation might become linear and straightforward.</li>
</ul>
<li><strong>Why This Matters:</strong></li>
<ul class="nested-list">
<li>Classical feature maps can be computationally expensive to calculate.</li>
<li>Quantum feature maps leverage quantum mechanics to potentially achieve exponential speedups in feature extraction and kernel calculation.</li>
<li>This is a core element of Quantum Support Vector Machines (QSVMs).</li>
</ul>
</ul>
<strong>Visual Suggestions:</strong>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-11" data-slide-number="11">
    <div class="slide-header">
        <span class="slide-number">11</span>
    </div>
    <h1 class="slide-title">Quantum Feature Maps: Encoding Data into Quantum States...</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Diagram:</strong> Illustrate the flow of data from input (x) -&gt; Quantum Feature Map -&gt; Quantum State |φ(x)⟩.</li>
<li><strong>Example Circuit:</strong> Show a simplified example of a parameterized quantum circuit used as a feature map (e.g., alternating layers of single-qubit rotations and CNOT gates).</li>
<li><strong>Hilbert Space Visualization:</strong>  Represent a low-dimensional analogy of Hilbert space, visually showing how data points are projected and separated in a higher-dimensional space compared to the original input space.</li>
</ul>
```

    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-12" data-slide-number="12">
    <div class="slide-header">
        <span class="slide-number">12</span>
    </div>
    <h1 class="slide-title">Quantum Kernels: The Heart of QSVM</h1>
    <div class="content">
        <p class="elegant-paragraph"><em>Quantum Kernels: The Heart of QSVM</em>
<ul class="elegant-list">
<li><strong>What is a Quantum Kernel?</strong></li>
<ul class="nested-list">
<li>Mathematically:  K(x, x') = |⟨φ(x) | φ(x')⟩|²</li>
<li>Represents the overlap (similarity) between quantum states |φ(x)⟩ and |φ(x')⟩, where x and x' are data points mapped to quantum states.</li>
</ul>
<li><strong>Why is it Important?</strong></li>
<ul class="nested-list">
<li><strong>Classical Hardness:</strong>  Calculating K(x, x') can be computationally intractable for certain quantum feature maps φ.</li>
<li><strong>Quantum Advantage:</strong>  A quantum computer can efficiently <em>estimate</em> this kernel value.</li>
<li><strong>Core of QSVM:</strong> This efficient quantum estimation provides the <em>potential speedup</em> compared to classical SVMs.</li>
</ul>
</ul></p>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-13" data-slide-number="13">
    <div class="slide-header">
        <span class="slide-number">13</span>
    </div>
    <h1 class="slide-title">Quantum Kernels: The Heart of QSVM (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Key Concepts:</strong></li>
<ul class="nested-list">
<li><strong>Quantum Feature Map (φ):</strong> Encodes classical data (x) into a quantum state |φ(x)⟩ using parameterized quantum circuits.  Think of it as projecting data into a high-dimensional Hilbert space.</li>
<li><strong>Kernel Estimation:</strong>  The quantum computer <em>estimates</em> the overlap between these quantum states to compute the kernel value.  This estimation is crucial!</li>
</ul>
<li><strong>Analogy:</strong></li>
<ul class="nested-list">
<li>Imagine trying to find the similarity between two complex paintings.  Classically, this could take a very long time.  A quantum computer provides a "shortcut" to quickly estimate how similar they are.</li>
</ul>
<li><strong>Visual Suggestion:</strong></li>
<ul class="nested-list">
<li>Diagram showing data points being mapped to quantum states via a quantum feature map, and then an illustration of the overlap between these states being calculated.  An overlay could visually indicate where the classical computation is hard, and where the quantum computer offers a potential speedup.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-14" data-slide-number="14">
    <div class="slide-header">
        <span class="slide-number">14</span>
    </div>
    <h1 class="slide-title">The Hybrid Approach: Quantum Kernel, Classical Optimization</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>The Hybrid Advantage: Quantum + Classical</strong></li>
<ul class="nested-list">
<li>QSVMs leverage the best of both worlds:</li>
<ul class="nested-list">
<li><strong>Quantum Core:</strong> Quantum computer efficiently calculates the kernel matrix.</li>
<ul class="nested-list">
<li>Addresses the quadratic scaling issue of classical kernel calculations.</li>
<li>Estimates kernel values that may be classically intractable.</li>
</ul>
<li><strong>Classical Solver:</strong> Classical SVM solver handles model training.</li>
<ul class="nested-list">
<li>Solves the quadratic programming problem to find optimal hyperplane.</li>
</ul>
</ul>
<li><strong>Workflow:</strong></li>
</ul>
</ul>
<ol class="elegant-list">
<li><strong>Encode:</strong> Classical data INLINE<em>CODE</em>0 -&gt; Quantum state INLINE<em>CODE</em>1</li>
<li><strong>Kernel:</strong> Quantum computer calculates  INLINE<em>CODE</em>2</li>
<li><strong>Train:</strong> Classical SVM uses INLINE<em>CODE</em>3 to train the model.</li>
</ol><ul class="elegant-list">
<ul class="nested-list">
<li><strong>Benefit:</strong> Combines potential quantum speedup with established classical algorithms.</li>
<li><strong>Example:</strong> Imagine a complex protein interaction dataset. A classical SVM struggles, but a QSVM can leverage quantum kernels to uncover hidden patterns.</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-15" data-slide-number="15">
    <div class="slide-header">
        <span class="slide-number">15</span>
    </div>
    <h1 class="slide-title">The Hybrid Approach: Quantum Kernel, Classical...</h1>
    <div class="content">
        <p class="elegant-paragraph"><strong>Visual Suggestions:</strong>
<ul class="elegant-list">
<ul class="nested-list">
<li><strong>Diagram:</strong> A flowchart illustrating the hybrid workflow (encode, quantum kernel, classical training).</li>
<li><strong>Iconography:</strong> Use icons to represent quantum computers, classical computers, and data flow.</li>
<li><strong>Equation Visualization:</strong> A simple representation of the kernel equation, highlighting the inner product.</li>
</ul>
</ul></p>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-16" data-slide-number="16">
    <div class="slide-header">
        <span class="slide-number">16</span>
    </div>
    <h1 class="slide-title">QSVM Workflow: From Data to Prediction</h1>
    <div class="content">
        <p class="elegant-paragraph">
```
markdown</p>

<h3 id="qsvm-workflow-from-data-to-prediction">QSVM Workflow: From Data to Prediction</h3>
<ul class="elegant-list">
<li><strong>Data Encoding:</strong></li>
<ul class="nested-list">
<li>Classical data (e.g., images, text vectors) transformed into quantum states.</li>
<li>Utilizes a quantum feature map: x -&gt; |Φ(x)⟩</li>
<li><em>Visual: Diagram showing classical data being input into a "Quantum Feature Map" box, with a quantum state |Φ(x)⟩ emerging.</em></li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-17" data-slide-number="17">
    <div class="slide-header">
        <span class="slide-number">17</span>
    </div>
    <h1 class="slide-title">QSVM Workflow: From Data to Prediction (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Kernel Computation (Quantum):</strong></li>
<ul class="nested-list">
<li>Quantum computer estimates the kernel values: K(x, x') = |⟨Φ(x) | Φ(x')⟩|^2</li>
<li>Represents the similarity between quantum states.</li>
<li>Potential for exponential speedup compared to classical kernel computation.</li>
<li><em>Visual: Graphic of a quantum circuit estimating the kernel value. Could show qubits and gates.</em></li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-18" data-slide-number="18">
    <div class="slide-header">
        <span class="slide-number">18</span>
    </div>
    <h1 class="slide-title">QSVM Workflow: From Data to Prediction (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Model Training (Classical):</strong></li>
<ul class="nested-list">
<li>A classical SVM solver uses the quantum-estimated kernel matrix.</li>
<li>Finds the optimal hyperplane to separate data in the feature space.</li>
<li>Hybrid approach: Quantum kernel, classical optimization.</li>
<li><em>Visual: Illustration of an SVM hyperplane separating data points, emphasizing that the kernel calculation was done on a quantum computer.</em></li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-19" data-slide-number="19">
    <div class="slide-header">
        <span class="slide-number">19</span>
    </div>
    <h1 class="slide-title">QSVM Workflow: From Data to Prediction (Part 4)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Prediction:</strong></li>
<ul class="nested-list">
<li>New, unseen data points are classified.</li>
<li>Requires another quantum kernel estimation with the trained model.</li>
<li>Output is the predicted class label (e.g., +1 or -1).</li>
<li><em>Visual:  Flowchart summarizing the entire process from input data to predicted class, highlighting the quantum kernel computation step.</em></li>
</ul>
</ul>
<strong>Key Takeaway:</strong> QSVM leverages quantum computing to efficiently compute kernel values, potentially accelerating SVM training and improving performance, especially with high-dimensional data.

<p class="elegant-paragraph">```
</p>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-20" data-slide-number="20">
    <div class="slide-header">
        <span class="slide-number">20</span>
    </div>
    <h1 class="slide-title">Advantages of QSVMs: Potential and Promise</h1>
    <div class="content">
        
<h3 id="advantages-of-qsvms-potential-and-promise">Advantages of QSVMs: Potential and Promise</h3>
<ul class="elegant-list">
<li><strong>Potential Speedup:</strong></li>
<ul class="nested-list">
<li>Leverages quantum computing for kernel computation, a major bottleneck in classical SVMs.</li>
<li>Especially beneficial for complex kernels and large datasets where classical SVM scaling becomes prohibitive (quadratic complexity).</li>
<li><em>Visual Suggestion: A graph comparing the computational complexity scaling of classical SVM vs. QSVM (showing QSVM potentially scaling better)</em></li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-21" data-slide-number="21">
    <div class="slide-header">
        <span class="slide-number">21</span>
    </div>
    <h1 class="slide-title">Advantages of QSVMs: Potential and Promise (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Enhanced Feature Representation:</strong></li>
<ul class="nested-list">
<li>Employs quantum feature maps to project data into richer, high-dimensional Hilbert spaces.</li>
<li>Potentially enables better separation of data, leading to improved classification performance.</li>
<li>Classical kernels might struggle to capture complex relationships present in data.</li>
<li><em>Visual Suggestion: A before/after diagram showing data separability improved by a quantum feature map compared to a classical kernel.</em></li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-22" data-slide-number="22">
    <div class="slide-header">
        <span class="slide-number">22</span>
    </div>
    <h1 class="slide-title">Advantages of QSVMs: Potential and Promise (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Naturally Suited for Complex Data:</strong></li>
<ul class="nested-list">
<li>Handles high-dimensional data more effectively than classical SVMs, crucial for tasks like genomics, image recognition, and financial modeling.</li>
<li>Data with numerous features or intricate relationships benefits most from quantum feature spaces.</li>
<li><em>Visual Suggestion: An icon array representing different types of high-dimensional data where QSVMs might excel (e.g., DNA strands, images, stock charts).</em></li>
</ul>
<li><strong>Important Note:</strong> While promising, current QSVM implementations are often hybrid (quantum kernel, classical optimization) and are subject to the limitations of NISQ (Noisy Intermediate-Scale Quantum) devices.</li>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-23" data-slide-number="23">
    <div class="slide-header">
        <span class="slide-number">23</span>
    </div>
    <h1 class="slide-title">Challenges and Limitations: Reality Check</h1>
    <div class="content">
        
<h3 id="challenges-and-limitations-reality-check">Challenges and Limitations: Reality Check</h3>
<ul class="elegant-list">
<li><strong>NISQ Device Limitations:</strong></li>
<ul class="nested-list">
<li>Limited Qubit Count: Constrains the size and complexity of problems solvable.</li>
<li>Short Coherence Times: Limits circuit depth and introduces noise.</li>
</ul>
<li><strong>Data Encoding Overhead:</strong></li>
<ul class="nested-list">
<li>Mapping Classical Data to Quantum States:  Can be computationally expensive and negate potential quantum speedups.</li>
<li>Efficient Encoding Schemes: Remain a key area of research (e.g., amplitude encoding vs. angle encoding).</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-24" data-slide-number="24">
    <div class="slide-header">
        <span class="slide-number">24</span>
    </div>
    <h1 class="slide-title">Challenges and Limitations: Reality Check (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Scalability Issues:</strong></li>
<ul class="nested-list">
<li>Training QSVMs on Large Datasets: Difficult with current quantum hardware. Quadratic Programming solvers become a bottleneck.</li>
<li>Need for Clever Data Reduction Techniques: To make problems tractable for near-term quantum computers.</li>
</ul>
<li><strong>Quantum Noise and Error Mitigation:</strong></li>
<ul class="nested-list">
<li>Noise in Quantum Computations: Significantly affects accuracy and reliability of results.</li>
<li>Error Mitigation Strategies: Required to reduce the impact of noise (e.g., zero-noise extrapolation, probabilistic error cancellation).  Adds complexity and overhead.</li>
<li>Example: Error rates can limit useful circuit depth to just a few layers.</li>
</ul>
</ul>
<strong>Visual Suggestions:</strong>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-25" data-slide-number="25">
    <div class="slide-header">
        <span class="slide-number">25</span>
    </div>
    <h1 class="slide-title">Challenges and Limitations: Reality Check (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>NISQ Device Illustration:</strong> A simplified diagram of a noisy quantum circuit with labeled limitations (e.g., qubit count, decoherence).</li>
<li><strong>Data Encoding Comparison:</strong> Visual representation comparing classical data, an inefficient quantum encoding, and a more efficient encoding.</li>
<li><strong>Scalability Chart:</strong> A graph showing the performance of QSVM training as dataset size increases, highlighting the limitations.</li>
<li><strong>Error Mitigation Example:</strong> A before/after visualization demonstrating the impact of error mitigation techniques on the accuracy of quantum computations.</li>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-26" data-slide-number="26">
    <div class="slide-header">
        <span class="slide-number">26</span>
    </div>
    <h1 class="slide-title">Applications of QSVMs: Where Can They Be Used?</h1>
    <div class="content">
        
<h3 id="applications-of-qsvms-where-can-they-be-used-">Applications of QSVMs: Where Can They Be Used?</h3>
<ul class="elegant-list">
<li><strong>Expanding the Reach of SVMs:</strong> QSVMs aim to enhance classical SVM performance, especially in scenarios involving high-dimensional data.</li>
<li><strong>Leveraging Existing SVM Applications:</strong> Look to areas where classical SVMs are already successful, particularly those bottlenecked by computational cost.</li>
<ul class="nested-list">
<li><strong>Bioinformatics:</strong> Drug discovery (identifying potential drug candidates), protein classification (predicting protein function).</li>
<ul class="nested-list">
<li><em>Visual Cue: Image of a protein structure or a drug molecule.</em></li>
</ul>
<li><strong>Financial Modeling:</strong> Fraud detection (identifying fraudulent transactions), credit risk assessment.</li>
<ul class="nested-list">
<li><em>Visual Cue: Graph of financial data or a security lock icon.</em></li>
</ul>
<li><strong>Image Recognition:</strong> Object detection, image classification, particularly with complex datasets.</li>
<ul class="nested-list">
<li><em>Visual Cue: Example images of objects being recognized or categorized.</em></li>
</ul>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-27" data-slide-number="27">
    <div class="slide-header">
        <span class="slide-number">27</span>
    </div>
    <h1 class="slide-title">Applications of QSVMs: Where Can They Be Used? (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Seeking Quantum Advantage:</strong> Explore applications where QSVM's potential speedup and superior feature representation can unlock significant advantages.</li>
<ul class="nested-list">
<li><strong>Consider:</strong> Problems where classical kernel methods struggle to scale or capture complex data patterns.</li>
<li><strong>Remember:</strong> The quantum kernel can capture complex relationships in the data that are classically hard to compute.</li>
</ul>
<li><strong>Focus on:</strong> High-dimensional data that benefits most from QSVM's enhanced feature mapping capabilities.</li>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-28" data-slide-number="28">
    <div class="slide-header">
        <span class="slide-number">28</span>
    </div>
    <h1 class="slide-title">Implementation Libraries: Tools for Exploring QSVMs</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Qiskit (IBM):</strong></li>
<ul class="nested-list">
<li>Open-source quantum computing framework.</li>
<li>Modules for building and experimenting with quantum machine learning models, including QSVMs.</li>
<li>[Visual: Qiskit logo, potentially with a code snippet example of QSVM implementation]</li>
</ul>
<li><strong>PennyLane (Xanadu):</strong></li>
<ul class="nested-list">
<li>Quantum machine learning library with a focus on differentiable programming.</li>
<li>Provides tools for creating and training hybrid quantum-classical models, including QSVMs.</li>
<li>[Visual: PennyLane logo, diagram illustrating the differentiable quantum programming approach]</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-29" data-slide-number="29">
    <div class="slide-header">
        <span class="slide-number">29</span>
    </div>
    <h1 class="slide-title">Implementation Libraries: Tools for Exploring QSVMs (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>TensorFlow Quantum (Google):</strong></li>
<ul class="nested-list">
<li>Integrates quantum computations with the TensorFlow machine learning framework.</li>
<li>Allows building hybrid quantum-classical models using TensorFlow's existing infrastructure.</li>
<li>[Visual: TensorFlow Quantum logo, architecture diagram showing the integration with TensorFlow]</li>
</ul>
<li><strong>Key Functionality:</strong></li>
<ul class="nested-list">
<li><strong>Quantum Kernel Estimation:</strong>  Efficiently calculate kernel values on quantum hardware, potentially offering speedup.  Classically calculating complex kernels scales quadratically.</li>
<li><strong>Data Encoding:</strong> Encode classical data into quantum states using feature maps.</li>
<li><strong>Model Training &amp; Prediction:</strong> Utilize QSVM components in hybrid workflows alongside classical SVM solvers.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-30" data-slide-number="30">
    <div class="slide-header">
        <span class="slide-number">30</span>
    </div>
    <h1 class="slide-title">Implementation Libraries: Tools for Exploring QSVMs (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Purpose:</strong>  These libraries provide the building blocks and tools for researchers and developers to:</li>
<ul class="nested-list">
<li>Experiment with QSVMs and other quantum machine learning algorithms.</li>
<li>Explore the potential benefits of quantum computing for classification tasks.</li>
<li>Contribute to the development of quantum machine learning techniques.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-31" data-slide-number="31">
    <div class="slide-header">
        <span class="slide-number">31</span>
    </div>
    <h1 class="slide-title">Conclusion: The Future of Quantum SVMs</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>QSVMs: A Quantum Boost for SVMs</strong></li>
<ul class="nested-list">
<li>Offer a promising avenue for enhancing classical SVM performance.</li>
<li>Leverage quantum computers to accelerate kernel calculations.</li>
</ul>
<li><strong>The Promise of Quantum Speedup</strong></li>
<ul class="nested-list">
<li>Potential for significant speedups in kernel computation, especially for complex, high-dimensional data.</li>
<li>Aim to overcome the quadratic scaling limitations of classical SVMs.</li>
</ul>
</ul>
    </div>
    
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-32" data-slide-number="32">
    <div class="slide-header">
        <span class="slide-number">32</span>
    </div>
    <h1 class="slide-title">Conclusion: The Future of Quantum SVMs (Part 2)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Current Landscape &amp; Challenges</strong></li>
<ul class="nested-list">
<li><strong>NISQ Era Limitations:</strong> Current quantum hardware restricts the size and complexity of QSVMs.</li>
<li><strong>Data Encoding Overhead:</strong> Efficient data encoding into quantum states remains a challenge.</li>
<li><strong>Scalability Issues:</strong> Scaling QSVMs to real-world datasets is an active area of research.</li>
</ul>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-33" data-slide-number="33">
    <div class="slide-header">
        <span class="slide-number">33</span>
    </div>
    <h1 class="slide-title">Conclusion: The Future of Quantum SVMs (Part 3)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>The Path Forward</strong></li>
<ul class="nested-list">
<li>Ongoing research and advancements in quantum hardware are crucial.</li>
<li>Exploration of novel quantum feature maps and kernel designs.</li>
<li>Hybrid quantum-classical algorithms are key to practical applications.</li>
<li>Libraries like Qiskit, PennyLane, and TensorFlow Quantum support QSVM development.</li>
</ul>
<li><strong>QSVMs: A Vital Step</strong></li>
<ul class="nested-list">
<li>Crucial exploration towards harnessing the full potential of quantum machine learning.</li>
</ul>
</ul>
<strong>Visual Suggestions:</strong>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

<div class="slide" id="slide-34" data-slide-number="34">
    <div class="slide-header">
        <span class="slide-number">34</span>
    </div>
    <h1 class="slide-title">Conclusion: The Future of Quantum SVMs (Part 4)</h1>
    <div class="content">
        <ul class="elegant-list">
<li><strong>Top Right:</strong> An illustration depicting a classical SVM hyperplane vs. a quantum feature map projecting data into a high-dimensional Hilbert space.</li>
<li><strong>Bottom Right:</strong> A timeline showing the evolution of quantum hardware and its impact on QSVM capabilities (NISQ era to Fault-Tolerant era).</li>
<li><strong>Throughout:</strong> Use color coding to differentiate between classical and quantum aspects (e.g., blue for classical, purple for quantum).</li>
</ul>
    </div>
    <div class="slide-part-indicator">Multi-part slide</div>
    <div class="slide-footer">
        <div class="progress-indicator">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
    </div>
</div>

    </div>

    <div class="controls">
        <button class="control-button" id="prev-button" title="Previous slide">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="control-button" id="next-button" title="Next slide">
            <i class="fas fa-chevron-right"></i>
        </button>
        <button class="control-button" id="fullscreen-button" title="Toggle fullscreen">
            <i class="fas fa-expand"></i>
        </button>
        <button class="control-button" id="zoom-in-button" title="Zoom in">
            <i class="fas fa-search-plus"></i>
        </button>
        <button class="control-button" id="zoom-out-button" title="Zoom out">
            <i class="fas fa-search-minus"></i>
        </button>
        <button class="control-button" id="reset-zoom-button" title="Reset zoom">
            <i class="fas fa-sync-alt"></i>
        </button>
    </div>

    <div class="theme-switcher">
        <button class="theme-button" id="theme-toggle">Change Theme</button>
    </div>

    <script>
        // Slide navigation functionality
        const slides = document.querySelectorAll('.slide');
        const totalSlides = 34;
        let currentSlide = 0;

        // Progress bar update
        function updateProgress() {
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                bar.style.width = `${((currentSlide + 1) / totalSlides) * 100}%`;
            });
        }

        // Show the current slide with elegant transitions
        function showSlide(index) {
            // Get previous slide index
            const prevIndex = currentSlide;

            // Hide all slides and remove transition classes
            slides.forEach(slide => {
                slide.classList.remove('active', 'prev', 'next');
            });

            // Add appropriate transition classes
            if (prevIndex !== index && prevIndex >= 0 && prevIndex < slides.length) {
                if (prevIndex < index) {
                    slides[prevIndex].classList.add('prev');
                } else {
                    slides[prevIndex].classList.add('next');
                }
            }

            // Show the current slide
            slides[index].classList.add('active');

            // Update progress
            updateProgress();

            // Reset zoom and auto-fit content for the new slide
            resetZoom();
            setTimeout(autoFitContent, 100);

            // Add animation delay to content elements
            const contentElements = slides[index].querySelectorAll('.content > *');
            contentElements.forEach((element, i) => {
                element.style.setProperty('--animation-order', i);
            });

            // Update document title with slide title
            const slideTitle = slides[index].querySelector('.slide-title').textContent;
            document.title = slideTitle + ' | Presentation';
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        function prevSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        // Theme switching functionality with elegant themes
        const themes = ['elegant-dark', 'elegant-light', 'royal-purple', 'ocean-blue', 'sunset'];
        let currentTheme = 0;

        function switchTheme() {
            currentTheme = (currentTheme + 1) % themes.length;
            document.body.setAttribute('data-theme', themes[currentTheme]);

            // Update CSS variables based on theme
            const themeColors = {
                "elegant-dark": {
                    "background": "linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
                    "text": "#ffffff",
                    "accent": "#0f3460",
                    "secondary": "#e94560",
                    "subtle_accent": "rgba(15, 52, 96, 0.2)",
                    "highlight": "#f1c40f",
                    "muted": "rgba(255, 255, 255, 0.7)",
                    "shadow": "rgba(0, 0, 0, 0.2)"
                },
                "elegant-light": {
                    "background": "linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%)",
                    "text": "#333333",
                    "accent": "#3498db",
                    "secondary": "#2ecc71",
                    "subtle_accent": "rgba(52, 152, 219, 0.1)",
                    "highlight": "#e74c3c",
                    "muted": "rgba(0, 0, 0, 0.6)",
                    "shadow": "rgba(0, 0, 0, 0.1)"
                },
                "royal-purple": {
                    "background": "linear-gradient(135deg, #2c2157 0%, #1e1646 100%)",
                    "text": "#f5f5f5",
                    "accent": "#8a4fff",
                    "secondary": "#ff7eb6",
                    "subtle_accent": "rgba(138, 79, 255, 0.2)",
                    "highlight": "#ffcf5c",
                    "muted": "rgba(245, 245, 245, 0.7)",
                    "shadow": "rgba(0, 0, 0, 0.3)"
                },
                "ocean-blue": {
                    "background": "linear-gradient(135deg, #f0f5f9 0%, #e4f0f5 100%)",
                    "text": "#1e2022",
                    "accent": "#1e5f74",
                    "secondary": "#133b5c",
                    "subtle_accent": "rgba(30, 95, 116, 0.1)",
                    "highlight": "#fcdab7",
                    "muted": "rgba(30, 32, 34, 0.7)",
                    "shadow": "rgba(19, 59, 92, 0.1)"
                },
                "sunset": {
                    "background": "linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
                    "text": "#ffffff",
                    "accent": "#ff9a3c",
                    "secondary": "#ff6a88",
                    "subtle_accent": "rgba(255, 154, 60, 0.2)",
                    "highlight": "#ffde7d",
                    "muted": "rgba(255, 255, 255, 0.7)",
                    "shadow": "rgba(0, 0, 0, 0.25)"
                }
            };

            // Apply theme with smooth transition
            document.body.style.transition = 'background 0.5s ease';

            // Get the selected theme
            const selectedTheme = themeColors[themes[currentTheme]];

            // Set all CSS variables
            document.documentElement.style.setProperty('--background', selectedTheme.background);
            document.documentElement.style.setProperty('--text-color', selectedTheme.text);
            document.documentElement.style.setProperty('--accent-color', selectedTheme.accent);
            document.documentElement.style.setProperty('--secondary-color', selectedTheme.secondary);
            document.documentElement.style.setProperty('--subtle-accent', selectedTheme.subtle_accent);
            document.documentElement.style.setProperty('--highlight-color', selectedTheme.highlight);
            document.documentElement.style.setProperty('--muted-text', selectedTheme.muted);
            document.documentElement.style.setProperty('--shadow-color', selectedTheme.shadow);

            // Format theme name for display
            const themeName = themes[currentTheme].split('-').map(word =>
                word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ');

            document.getElementById('theme-toggle').textContent = themeName;

            // Add a visual feedback for theme change
            const feedback = document.createElement('div');
            feedback.className = 'theme-change-feedback';
            feedback.textContent = 'Theme: ' + themeName;
            feedback.style.position = 'fixed';
            feedback.style.top = '50%';
            feedback.style.left = '50%';
            feedback.style.transform = 'translate(-50%, -50%)';
            feedback.style.background = selectedTheme.accent;
            feedback.style.color = selectedTheme.text;
            feedback.style.padding = '1rem 2rem';
            feedback.style.borderRadius = '30px';
            feedback.style.boxShadow = '0 4px 20px ' + selectedTheme.shadow;
            feedback.style.zIndex = '1000';
            feedback.style.opacity = '0';
            feedback.style.transition = 'opacity 0.3s ease';

            document.body.appendChild(feedback);

            // Show and hide feedback
            setTimeout(() => { feedback.style.opacity = '1'; }, 50);
            setTimeout(() => {
                feedback.style.opacity = '0';
                setTimeout(() => { document.body.removeChild(feedback); }, 300);
            }, 1500);
        }

        // Fullscreen functionality
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log(`Error attempting to enable fullscreen: ${err.message}`);
                });
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        }

        // Zoom functionality
        let currentZoom = 1;
        const MIN_ZOOM = 0.5;
        const MAX_ZOOM = 2;
        const ZOOM_STEP = 0.1;

        function setZoom(zoomLevel) {
            currentZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, zoomLevel));
            document.querySelectorAll('.content').forEach(content => {
                content.style.transform = `scale(${currentZoom})`;
                content.style.transformOrigin = 'top left';
            });
        }

        function zoomIn() {
            setZoom(currentZoom + ZOOM_STEP);
        }

        function zoomOut() {
            setZoom(currentZoom - ZOOM_STEP);
        }

        function resetZoom() {
            setZoom(1);
        }

        // Auto-fit content if it overflows
        function autoFitContent() {
            const activeSlide = document.querySelector('.slide.active');
            if (!activeSlide) return;

            const content = activeSlide.querySelector('.content');
            const slideHeight = activeSlide.clientHeight;
            const titleHeight = activeSlide.querySelector('.slide-title').offsetHeight;
            const footerHeight = activeSlide.querySelector('.slide-footer').offsetHeight;
            const availableHeight = slideHeight - titleHeight - footerHeight - 120; // 120px for padding

            if (content.scrollHeight > availableHeight) {
                // Content is too tall, calculate zoom level to fit
                const zoomFactor = availableHeight / content.scrollHeight;
                if (zoomFactor < 1) {
                    setZoom(Math.max(MIN_ZOOM, zoomFactor));
                }
            }
        }

        // Initialize presentation
        function initPresentation() {
            // Show first slide
            showSlide(currentSlide);

            // Add event listeners
            document.getElementById('next-button').addEventListener('click', nextSlide);
            document.getElementById('prev-button').addEventListener('click', prevSlide);
            document.getElementById('fullscreen-button').addEventListener('click', toggleFullscreen);
            document.getElementById('theme-toggle').addEventListener('click', switchTheme);
            document.getElementById('zoom-in-button').addEventListener('click', zoomIn);
            document.getElementById('zoom-out-button').addEventListener('click', zoomOut);
            document.getElementById('reset-zoom-button').addEventListener('click', resetZoom);

            // Add keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowRight' || e.key === ' ' || e.key === 'n') {
                    nextSlide();
                } else if (e.key === 'ArrowLeft' || e.key === 'p') {
                    prevSlide();
                } else if (e.key === 'f') {
                    toggleFullscreen();
                } else if (e.key === 't') {
                    switchTheme();
                } else if (e.key === '+' || e.key === '=') {
                    zoomIn();
                } else if (e.key === '-') {
                    zoomOut();
                } else if (e.key === '0') {
                    resetZoom();
                }
            });

            // Animate content elements with delay
            const contentElements = document.querySelectorAll('.content > *');
            contentElements.forEach((element, index) => {
                element.classList.add('fade-in');
                element.style.animationDelay = `${index * 0.2}s`;
            });

            // Set initial theme
            switchTheme(); // Apply the first theme

            // Initialize syntax highlighting for code blocks
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });

            // Auto-fit content if needed
            setTimeout(autoFitContent, 500);

            // Add resize listener to handle window size changes
            window.addEventListener('resize', function() {
                resetZoom();
                setTimeout(autoFitContent, 200);
            });
        }

        // Run initialization when DOM is loaded
        document.addEventListener('DOMContentLoaded', initPresentation);
    </script>
</body>
</html>
